"""
数据加载器修复工具

这个工具提供了一个修改后的get_data_loaders函数，
可以替代原始脚本中的get_data_loaders函数，并自动修复SVHN数据集的标签问题。
"""

import torch
import torchvision
from torch.utils.data import DataLoader
from torchvision import transforms

def fix_svhn_labels(dataset):
    """
    修正SVHN标签，确保它们在0-9范围内
    兼容多种标签存储格式，包括PyTorch 0.4+和1.0+版本
    """
    # 处理SVHN的不同标签存储格式
    if hasattr(dataset, 'labels'):
        # 早期PyTorch版本
        if isinstance(dataset.labels, torch.Tensor):
            dataset.labels[dataset.labels == 10] = 0
        else:
            dataset.labels[dataset.labels == 10] = 0
    elif hasattr(dataset, 'targets'):
        # 较新的PyTorch版本
        if isinstance(dataset.targets, torch.Tensor):
            dataset.targets[dataset.targets == 10] = 0
        else:
            dataset.targets[dataset.targets == 10] = 0
    elif hasattr(dataset, 'target'):
        # 某些自定义数据集
        if isinstance(dataset.target, torch.Tensor):
            dataset.target[dataset.target == 10] = 0
        else:
            dataset.target[dataset.target == 10] = 0
    elif hasattr(dataset, '_labels'):
        # SVHN特有格式
        if isinstance(dataset._labels, torch.Tensor):
            dataset._labels[dataset._labels == 10] = 0
        else:
            dataset._labels[dataset._labels == 10] = 0
    # 处理SVHN在内存映射模式下可能的label格式
    elif hasattr(dataset, 'data') and isinstance(dataset.data, dict) and 'labels' in dataset.data:
        dataset.data['labels'][dataset.data['labels'] == 10] = 0
    else:
        print("警告: 无法识别的SVHN标签格式，请手动检查SVHN数据集的标签属性")
    
    return dataset

def get_data_loaders(dataset_name, data_path, batch_size, num_workers):
    """
    修改后的get_data_loaders函数，自动修复SVHN数据集的标签问题
    
    参数与原始函数相同，可以直接替代
    """
    if dataset_name == 'cifar10':
        num_classes = 10
        transform_train = transforms.Compose([transforms.RandomCrop(32, padding=4), transforms.RandomHorizontalFlip(), transforms.ToTensor(), transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))])
        transform_test = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))])
        trainset = torchvision.datasets.CIFAR10(root=data_path, train=True, download=True, transform=transform_train)
        testset = torchvision.datasets.CIFAR10(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'cifar100':
        num_classes = 100
        transform_train = transforms.Compose([transforms.RandomCrop(32, padding=4), transforms.RandomHorizontalFlip(), transforms.ToTensor(), transforms.Normalize((0.5071, 0.4867, 0.4408), (0.2675, 0.2565, 0.2761))])
        transform_test = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5071, 0.4867, 0.4408), (0.2675, 0.2565, 0.2761))])
        trainset = torchvision.datasets.CIFAR100(root=data_path, train=True, download=True, transform=transform_train)
        testset = torchvision.datasets.CIFAR100(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'svhn':
        num_classes = 10
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4377, 0.4438, 0.4728), (0.1980, 0.2010, 0.1970))
        ])
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4377, 0.4438, 0.4728), (0.1980, 0.2010, 0.1970))
        ])
        trainset = torchvision.datasets.SVHN(root=data_path, split='train', download=True, transform=transform_train)
        testset = torchvision.datasets.SVHN(root=data_path, split='test', download=True, transform=transform_test)
        
        # ===== 修复SVHN标签 =====
        print("正在修复SVHN数据集标签...")
        trainset = fix_svhn_labels(trainset)
        testset = fix_svhn_labels(testset)
        
        # 打印标签分布以确认修复成功
        if hasattr(trainset, 'labels'):
            unique_labels = sorted(set(trainset.labels.tolist() if isinstance(trainset.labels, torch.Tensor) else trainset.labels.tolist()))
            print(f"SVHN训练集标签分布 (修复后): {unique_labels}")  # 应该是0-9，不含10
        elif hasattr(trainset, 'targets'):
            unique_labels = sorted(set(trainset.targets.tolist() if isinstance(trainset.targets, torch.Tensor) else trainset.targets.tolist()))
            print(f"SVHN训练集标签分布 (修复后): {unique_labels}")  # 应该是0-9，不含10
        print("SVHN数据集标签修复完成！")
        # =========================
        
    elif dataset_name == 'fashion_mnist':
        num_classes = 10
        transform_train = transforms.Compose([
            transforms.Resize(32),
            transforms.Grayscale(num_output_channels=3),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])
        transform_test = transforms.Compose([
            transforms.Resize(32),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])
        trainset = torchvision.datasets.FashionMNIST(root=data_path, train=True, download=True, transform=transform_train)
        testset = torchvision.datasets.FashionMNIST(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'caltech101':
        import os
        import logging
        # 为 Caltech101 定义图像变换，复用ImageNet的参数
        transform = transforms.Compose([
             transforms.Grayscale(num_output_channels=3), # 确保所有图像都是3通道
             transforms.Resize((224, 224)), # 调整图像大小以适应常用模型
             transforms.ToTensor(),
             transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ])
        
        # 使用 ImageFolder 加载数据集，它更通用
        # 假设 data_path 指向 '.../data'，而 'caltech101' 在其中
        dataset_path = os.path.join(data_path, 'caltech101')
        if not os.path.isdir(dataset_path):
            # 如果上面的路径不对，直接使用用户提供的绝对路径
            dataset_path = '/home1/guohm/disperse/data/caltech101'
            logging.warning(f"Default dataset path not found, falling back to absolute path: {dataset_path}")

        dataset = torchvision.datasets.ImageFolder(root=dataset_path, transform=transform)
        
        # 使用固定的随机种子来确保每次划分都一样，便于复现
        train_size = int(0.8 * len(dataset))
        test_size = len(dataset) - train_size
        trainset, testset = torch.utils.data.random_split(
            dataset,
            [train_size, test_size],
            generator=torch.Generator().manual_seed(42) # 固定种子
        )
        num_classes = len(dataset.classes)
        logging.info(f"Caltech101 loaded with {num_classes} classes and split into {train_size} train / {test_size} test samples.")
    else:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    trainloader = DataLoader(trainset, batch_size=batch_size, shuffle=True, num_workers=num_workers, pin_memory=True)
    testloader = DataLoader(testset, batch_size=batch_size, shuffle=False, num_workers=num_workers, pin_memory=True)
    return trainloader, testloader, num_classes

# 使用说明
"""
使用方法:

1. 在原始脚本的开头添加:
from fix_data_loaders import get_data_loaders

2. 删除或注释掉原来的get_data_loaders函数定义

3. 重新运行脚本

这样，SVHN数据集的标签问题就会在数据加载阶段自动修复，不需要修改其他代码。
""" 