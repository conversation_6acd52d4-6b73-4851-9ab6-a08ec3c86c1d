from __future__ import print_function
import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import torch.optim as optim
from torchvision import datasets, transforms
from torch.utils.data import DataLoader

# 导入SVHN数据集修复工具
from fix_data_loaders import get_data_loaders  # 使用修复后的数据加载函数

from models.wideresnet import *
from models.resnet import *
from trades import trades_loss 