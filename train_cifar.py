import argparse
import logging
import sys
import time
import math
import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.autograd import Variable
from torchvision import datasets, transforms
from torch.utils.data import DataLoader

# 导入模型
from models import get_model
# 保留原始模型导入以确保兼容性
from wideresnet import WideResNet
from preactresnet import PreActResNet18

from utils import *
from fix_svhn import fix_svhn_labels

# 定义全局变量
upper_limit = 1.0
lower_limit = 0.0

# 不同数据集的均值和标准差
cifar10_mean = (0.4914, 0.4822, 0.4465)
cifar10_std = (0.2023, 0.1994, 0.2010)
cifar100_mean = (0.5071, 0.4867, 0.4408)
cifar100_std = (0.2675, 0.2565, 0.2761)
svhn_mean = (0.4377, 0.4438, 0.4728)
svhn_std = (0.1980, 0.2010, 0.1970)
fashion_mnist_mean = (0.5,)  # 修改为单通道
fashion_mnist_std = (0.5,)   # 修改为单通道

# 不再需要NormalizeModel类
# class NormalizeModel(nn.Module):
#     def __init__(self, model, mean, std):
#         super(NormalizeModel, self).__init__()
#         self.model = model
#         self.mean = torch.tensor(mean).view(1, -1, 1, 1).cuda()
#         self.std = torch.tensor(std).view(1, -1, 1, 1).cuda()

#     def forward(self, x):
#         # 归一化输入
#         x_normalized = (x - self.mean) / self.std
#         return self.model(x_normalized)

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--batch-size', default=128, type=int)
    parser.add_argument('--test-batch-size', default=128, type=int)
    parser.add_argument('--data-dir', default='./data', type=str)
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr-max', default=0.1, type=float)
    parser.add_argument('--lr-schedule', default='piecewise', choices=['superconverge', 'piecewise', 'linear', 'onedrop', 'multipledecay', 'cosine'])
    parser.add_argument('--lr-one-drop', default=0.01, type=float)
    parser.add_argument('--lr-drop-epoch', default=100, type=int)
    parser.add_argument('--dataset', default='cifar10', type=str, choices=['cifar10', 'cifar100', 'svhn', 'fashion_mnist', 'caltech101'])
    parser.add_argument('--attack', default='pgd', type=str, choices=['pgd', 'fgsm', 'free', 'none'])
    parser.add_argument('--epsilon', default=8, type=int)
    parser.add_argument('--pgd-alpha', default=2, type=float)
    parser.add_argument('--attack-iters', default=20, type=int)
    parser.add_argument('--restarts', default=1, type=int)
    parser.add_argument('--norm', default='l_inf', type=str, choices=['l_inf', 'l_2'])
    parser.add_argument('--fname', default='cifar_model', type=str)
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--model', default='ResNet18', type=str, choices=['ResNet18', 'PreActResNet18', 'WideResNet', 'wide_resnet34_10', 'ResNet50', 'DenseNet121'])
    parser.add_argument('--eval', action='store_true')
    parser.add_argument('--val', action='store_true', help='使用验证集')
    parser.add_argument('--no-dp', action='store_true', help='禁用DataParallel')
    return parser.parse_args()

def clamp(X, lower_limit, upper_limit):
    return torch.max(torch.min(X, upper_limit), lower_limit)

def mixup_data(x, y, alpha=1.0):
    '''Returns mixed inputs, pairs of targets, and lambda'''
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).cuda()

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def attack_pgd(model, X, y, epsilon, alpha, attack_iters, restarts, norm, early_stop=False,
               mixup=False, y_a=None, y_b=None, lam=None):
    # 在原始像素空间进行PGD攻击，然后再归一化输入模型
    # 根据数据集选择合适的均值和标准差进行归一化
    def normalize(X, dataset='cifar10'):
        if dataset == 'cifar10':
            mean = cifar10_mean
            std = cifar10_std
        elif dataset == 'cifar100':
            mean = cifar100_mean
            std = cifar100_std
        elif dataset == 'svhn':
            mean = svhn_mean
            std = svhn_std
        elif dataset == 'fashion_mnist':
            mean = fashion_mnist_mean
            std = fashion_mnist_std
        else:
            mean = (0.485, 0.456, 0.406)
            std = (0.229, 0.224, 0.225)
        
        return (X - torch.tensor(mean).view(1, -1, 1, 1).cuda()) / torch.tensor(std).view(1, -1, 1, 1).cuda()
    
    # 获取当前数据集类型
    dataset = args.dataset
    
    max_loss = torch.zeros(y.shape[0]).cuda()
    max_delta = torch.zeros_like(X).cuda()
    for _ in range(restarts):
        delta = torch.zeros_like(X).cuda()
        if norm == "l_inf":
            delta.uniform_(-epsilon, epsilon)
        elif norm == "l_2":
            delta.normal_()
            delta = delta.renorm(p=2, dim=1, maxnorm=epsilon)
        else:
            raise ValueError
        delta.requires_grad = True
        for _ in range(attack_iters):
            # 归一化后输入模型
            output = model(normalize(X + delta, dataset))
            if early_stop:
                index = torch.where(output.max(1)[1] == y)[0]
            else:
                index = slice(None,None,None)
            if not isinstance(index, slice) and len(index) == 0:
                break
            if mixup:
                criterion = nn.CrossEntropyLoss()
                loss = mixup_criterion(criterion, model(normalize(X+delta, dataset)), y_a, y_b, lam)
            else:
                loss = F.cross_entropy(output, y)
            loss.backward()
            grad = delta.grad.detach()
            d = delta[index, :, :, :]
            g = grad[index, :, :, :]
            x = X[index, :, :, :]
            if norm == "l_inf":
                d = torch.clamp(d + alpha * torch.sign(g), -epsilon, epsilon)
            elif norm == "l_2":
                g_norm = torch.norm(g.view(g.shape[0],-1),dim=1).view(-1,1,1,1)
                scaled_g = g/(g_norm + 1e-10)
                d = (d + scaled_g*alpha).view(d.size(0),-1).renorm(p=2,dim=0,maxnorm=epsilon).view_as(d)
            # 确保扰动后的图像在[0,1]范围内
            d = torch.clamp(d, 0 - x, 1 - x)
            delta.data[index, :, :, :] = d
            delta.grad.zero_()
        if mixup:
            criterion = nn.CrossEntropyLoss(reduction='none')
            all_loss = mixup_criterion(criterion, model(normalize(X+delta, dataset)), y_a, y_b, lam)
        else:
            all_loss = F.cross_entropy(model(normalize(X+delta, dataset)), y, reduction='none')
        max_delta[all_loss >= max_loss] = delta.detach()[all_loss >= max_loss]
        max_loss = torch.max(max_loss, all_loss)
    return max_delta

def get_data_loaders(dataset_name, data_path, batch_size, num_workers):
    """使用与提供的代码相同的方式加载数据，并在transform中进行归一化"""
    if dataset_name == 'cifar10':
        num_classes = 10
        transform_train = transforms.Compose([
            transforms.RandomCrop(32, padding=4), 
            # 移除RandomHorizontalFlip，保持与验证集一致
            # transforms.RandomHorizontalFlip(), 
            transforms.ToTensor(),
            transforms.Normalize(cifar10_mean, cifar10_std)  # 添加归一化到transform中
        ])
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(cifar10_mean, cifar10_std)  # 添加归一化到transform中
        ])
        trainset = datasets.CIFAR10(root=data_path, train=True, download=True, transform=transform_train)
        testset = datasets.CIFAR10(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'cifar100':
        num_classes = 100
        transform_train = transforms.Compose([
            transforms.RandomCrop(32, padding=4), 
            # 移除RandomHorizontalFlip，保持与验证集一致
            # transforms.RandomHorizontalFlip(), 
            transforms.ToTensor(),
            transforms.Normalize(cifar100_mean, cifar100_std)  # 添加归一化到transform中
        ])
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(cifar100_mean, cifar100_std)  # 添加归一化到transform中
        ])
        trainset = datasets.CIFAR100(root=data_path, train=True, download=True, transform=transform_train)
        testset = datasets.CIFAR100(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'svhn':
        num_classes = 10
        transform_train = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(svhn_mean, svhn_std)  # 添加归一化到transform中
        ])
        transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(svhn_mean, svhn_std)  # 添加归一化到transform中
        ])
        trainset = datasets.SVHN(root=data_path, split='train', download=True, transform=transform_train)
        testset = datasets.SVHN(root=data_path, split='test', download=True, transform=transform_test)
        # 修复SVHN标签问题
        trainset = fix_svhn_labels(trainset)
        testset = fix_svhn_labels(testset)
    elif dataset_name == 'fashion_mnist':
        num_classes = 10
        transform_train = transforms.Compose([
            transforms.Resize(32),
            # 移除 Grayscale 转换，保持单通道
            # 移除 RandomHorizontalFlip，保持与验证集一致
            transforms.ToTensor(),
            transforms.Normalize(fashion_mnist_mean, fashion_mnist_std)  # 添加归一化到transform中
        ])
        transform_test = transforms.Compose([
            transforms.Resize(32),
            # 移除 Grayscale 转换，保持单通道
            transforms.ToTensor(),
            transforms.Normalize(fashion_mnist_mean, fashion_mnist_std)  # 添加归一化到transform中
        ])
        trainset = datasets.FashionMNIST(root=data_path, train=True, download=True, transform=transform_train)
        testset = datasets.FashionMNIST(root=data_path, train=False, download=True, transform=transform_test)
    elif dataset_name == 'caltech101':
        # Caltech101 数据集处理
        import logging
        transform = transforms.Compose([
            transforms.Grayscale(num_output_channels=3), # 确保所有图像都是3通道
            transforms.Resize((224, 224)), # 调整图像大小以适应常用模型
            transforms.ToTensor(),
            # 移除归一化步骤，在模型前向传播时处理
        ])
        
        dataset_path = os.path.join(data_path, 'caltech101')
        if not os.path.isdir(dataset_path):
            dataset_path = '/home1/guohm/disperse/data/caltech101'
            logging.warning(f"默认数据集路径未找到，使用绝对路径: {dataset_path}")

        dataset = datasets.ImageFolder(root=dataset_path, transform=transform)
        
        train_size = int(0.8 * len(dataset))
        test_size = len(dataset) - train_size
        trainset, testset = torch.utils.data.random_split(
            dataset,
            [train_size, test_size],
            generator=torch.Generator().manual_seed(42) # 固定种子
        )
        num_classes = len(dataset.classes)
        logging.info(f"Caltech101 加载完成，共{num_classes}个类别，分为{train_size}个训练样本和{test_size}个测试样本")
    else:
        raise ValueError(f"不支持的数据集: {dataset_name}")
    
    # 处理验证集
    if dataset_name in ['cifar10', 'cifar100', 'svhn', 'fashion_mnist'] and args.val:
        # 尝试加载预先生成的验证集划分
        val_split_file = f"{dataset_name}_validation_split.pth"
        logging.info(f"尝试加载预先生成的{dataset_name}验证集划分...")
        
        # 创建一个使用测试集变换的新数据集实例用于验证集
        if dataset_name == 'cifar10':
            valset = datasets.CIFAR10(root=data_path, train=True, download=True, transform=transform_test)
        elif dataset_name == 'cifar100':
            valset = datasets.CIFAR100(root=data_path, train=True, download=True, transform=transform_test)
        elif dataset_name == 'svhn':
            valset = datasets.SVHN(root=data_path, split='train', download=True, transform=transform_test)
            valset = fix_svhn_labels(valset)
        elif dataset_name == 'fashion_mnist':
            valset = datasets.FashionMNIST(root=data_path, train=True, download=True, transform=transform_test)
        
        if os.path.exists(val_split_file):
            # 加载预先生成的划分
            split_data = torch.load(val_split_file)
            train_indices = split_data['train_indices']
            val_indices = split_data['val_indices']
            logging.info(f"成功加载{dataset_name}验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        else:
            logging.info(f"预设验证集划分文件{val_split_file}不存在，创建新的划分...")
            train_size = int(0.9 * len(trainset))
            val_size = len(trainset) - train_size
            indices = list(range(len(trainset)))
            np.random.shuffle(indices)
            train_indices = indices[:train_size]
            val_indices = indices[train_size:]
            
            # 保存划分
            torch.save({
                'train_indices': train_indices,
                'val_indices': val_indices,
                'val_size': val_size
            }, val_split_file)
            logging.info(f"创建并保存新的验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        
        from torch.utils.data import Subset
        train_dataset = Subset(trainset, train_indices)
        val_dataset = Subset(valset, val_indices)
        
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        return train_loader, val_loader, test_loader, num_classes
    else:
        # 不使用验证集
        train_loader = DataLoader(
            trainset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        return train_loader, None, test_loader, num_classes

def main():
    args = get_args()

    if not os.path.exists(args.fname):
        os.makedirs(args.fname)

    logger = logging.getLogger(__name__)
    logging.basicConfig(
        format='[%(asctime)s] - %(message)s',
        datefmt='%Y/%m/%d %H:%M:%S',
        level=logging.DEBUG,
        handlers=[
            logging.FileHandler(os.path.join(args.fname, 'eval.log' if args.eval else 'output.log')),
            logging.StreamHandler()
        ])

    logger.info(args)

    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)

    # 加载数据
    if args.val:
        train_loader, val_loader, test_loader, num_classes = get_data_loaders(
            args.dataset, args.data_dir, args.batch_size, num_workers=2
        )
    else:
        train_loader, _, test_loader, num_classes = get_data_loaders(
            args.dataset, args.data_dir, args.batch_size, num_workers=2
        )
        val_loader = None

    # 定义模型
    if args.model == 'ResNet18':
        model = get_model('ResNet18', num_classes=num_classes)
    elif args.model == 'ResNet50':
        model = get_model('ResNet50', num_classes=num_classes)
    elif args.model == 'DenseNet121':
        model = get_model('DenseNet121', num_classes=num_classes)
    elif args.model == 'PreActResNet18':
        model = get_model('PreActResNet18', num_classes=num_classes)
    elif args.model == 'WideResNet':
        model = get_model('WideResNet34_10', num_classes=num_classes)
    elif args.model == 'wide_resnet34_10':
        model = get_model('WideResNet34_10', num_classes=num_classes)
    else:
        raise ValueError("未知模型类型")

    # 将模型移至GPU
    if args.no_dp or torch.cuda.device_count() == 1:
        model = model.cuda()
    else:
        model = nn.DataParallel(model).cuda()
    
    # 不再需要使用NormalizeModel包装原始模型
    # 根据数据集选择合适的均值和标准差
    # if args.dataset == 'cifar10':
    #     mean = cifar10_mean
    #     std = cifar10_std
    # elif args.dataset == 'cifar100':
    #     mean = cifar100_mean
    #     std = cifar100_std
    # elif args.dataset == 'svhn':
    #     mean = svhn_mean
    #     std = svhn_std
    # elif args.dataset == 'fashion_mnist':
    #     mean = fashion_mnist_mean
    #     std = fashion_mnist_std
    # else:  # caltech101和其他数据集
    #     mean = (0.485, 0.456, 0.406)
    #     std = (0.229, 0.224, 0.225)
    
    # # 使用NormalizeModel包装原始模型
    # model = NormalizeModel(model, mean, std)

    # 设置优化器
    optimizer = optim.SGD(model.parameters(), lr=args.lr_max, momentum=0.9, weight_decay=5e-4)

    # 将epsilon和alpha转换为[0,1]范围
    epsilon = args.epsilon / 255.
    pgd_alpha = args.pgd_alpha / 255.

    # 定义调度器
    if args.lr_schedule == 'piecewise':
        def lr_schedule(t):
            if t < 100:
                return args.lr_max
            elif t < 150:
                return args.lr_max * 0.1
            else:
                return args.lr_max * 0.01
    elif args.lr_schedule == 'linear':
        def lr_schedule(t):
            return args.lr_max * (1 - t / args.epochs)
    elif args.lr_schedule == 'cosine':
        def lr_schedule(t):
            return args.lr_max * 0.5 * (1 + np.cos(t / args.epochs * np.pi))
    elif args.lr_schedule == 'onedrop':
        def lr_schedule(t):
            if t < args.lr_drop_epoch:
                return args.lr_max
            else:
                return args.lr_one_drop
    elif args.lr_schedule == 'multipledecay':
        def lr_schedule(t):
            return args.lr_max * 0.1 ** (t // 50)
    
    # 训练/评估循环

    # 保存初始状态
    if not args.eval:
        torch.save(model.state_dict(), os.path.join(args.fname, 'model_init.pth'))
        torch.save(optimizer.state_dict(), os.path.join(args.fname, 'opt_init.pth'))

    # 记录最佳准确率
    best_test_robust_acc = 0
    best_val_robust_acc = 0

    # 评估模式
    if args.eval:
        # 加载模型
        model.eval()
        # 在测试集上评估
        test_loss, test_acc = eval_test(model, test_loader, args)
        print(f"测试准确率: {test_acc:.2f}%")
        return

    # 训练模式
    start_epoch = 0
    for epoch in range(start_epoch, args.epochs):
        # 调整学习率
        lr = lr_schedule(epoch)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        
        # 训练一个epoch
        model.train()
        train_loss = 0
        train_acc = 0
        train_robust_loss = 0
        train_robust_acc = 0
        train_n = 0
        
        for i, (X, y) in enumerate(train_loader):
            X, y = X.cuda(), y.cuda()
            
            # 对抗训练
            if args.attack == 'pgd':
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                delta = delta.detach()
            elif args.attack == 'none':
                delta = torch.zeros_like(X)

            output = model(X + delta)
            loss = F.cross_entropy(output, y)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * y.size(0)
            train_acc += (output.max(1)[1] == y).sum().item()
            # 添加训练鲁棒性指标计算
            if args.attack != 'none':
                train_robust_loss += loss.item() * y.size(0)
                train_robust_acc += (output.max(1)[1] == y).sum().item()
            train_n += y.size(0)

            # 打印进度
            if (i + 1) % 100 == 0:
                print(f'Epoch: {epoch}, Step: {i+1}/{len(train_loader)}, Loss: {loss.item():.4f}')

        # 评估阶段
        model.eval()
        
        # 不评估测试集，只评估验证集
        if val_loader is not None:
            val_loss = 0
            val_acc = 0
            val_robust_loss = 0
            val_robust_acc = 0
            val_n = 0
            
            for i, (X, y) in enumerate(val_loader):
                X, y = X.cuda(), y.cuda()
                
                # 原始准确率
                with torch.no_grad():
                    output = model(X)
                    loss = F.cross_entropy(output, y)
                val_loss += loss.item() * y.size(0)
                val_acc += (output.max(1)[1] == y).sum().item()
                
                # 鲁棒性评估
                if args.attack != 'none':
                    delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                    with torch.no_grad():
                        output = model(X + delta)
                        robust_loss = F.cross_entropy(output, y)
                        val_robust_loss += robust_loss.item() * y.size(0)
                        val_robust_acc += (output.max(1)[1] == y).sum().item()
                
                val_n += y.size(0)

            # 计算验证集指标
            val_loss /= val_n
            val_acc = 100.0 * val_acc / val_n
            if args.attack != 'none':
                val_robust_loss /= val_n
                val_robust_acc = 100.0 * val_robust_acc / val_n
        
        # 计算指标
        train_loss /= train_n
        train_acc = 100.0 * train_acc / train_n
        
        # 不计算测试集指标
        test_loss = 0
        test_acc = 0
        test_robust_loss = 0
        test_robust_acc = 0
        
        if args.attack != 'none':
            train_robust_loss /= train_n
            train_robust_acc = 100.0 * train_robust_acc / train_n
        
        # 打印结果
        if val_loader is not None:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【验证指标】 Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【验证鲁棒】 Loss: {val_robust_loss:.4f}, Acc: {val_robust_acc:.2f}%")
        else:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【测试指标】 Loss: {test_loss:.4f}, Acc: {test_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【测试鲁棒】 Loss: {test_robust_loss:.4f}, Acc: {test_robust_acc:.2f}%")
        
        # 保存模型检查点
        torch.save(model.state_dict(), os.path.join(args.fname, f'model_epoch{epoch}.pth'))
        
        # 保存最佳模型
        if args.attack != 'none':
            # 根据验证集鲁棒准确率选择最佳模型
            if val_loader is not None and val_robust_acc > best_val_robust_acc:
                best_val_robust_acc = val_robust_acc
                torch.save({
                    'state_dict': model.state_dict(),
                    'val_robust_acc': best_val_robust_acc,
                    'epoch': epoch,
                }, os.path.join(args.fname, 'model_best.pth'))
            # 不再根据测试集选择最佳模型

if __name__ == "__main__":
    args = get_args()
    main()
