#!/bin/bash

# 默认参数
MODEL="resnet18"
EPOCHS=200
BATCH_SIZE=128
LR=0.01
GPU_ID=0
VAL=""
LR_SCHEDULE="step"

# 解析命令行参数
if [ $# -ge 1 ]; then
    MODEL=$1
fi

if [ $# -ge 2 ]; then
    EPOCHS=$2
fi

if [ $# -ge 3 ]; then
    BATCH_SIZE=$3
fi

if [ $# -ge 4 ]; then
    LR=$4
fi

if [ $# -ge 5 ]; then
    GPU_ID=$5
fi

# 检查是否有--val标志
for arg in "$@"; do
    if [ "$arg" == "--val" ]; then
        VAL="--val"
        break
    fi
done

# 检查是否指定了学习率调度
for arg in "$@"; do
    if [[ "$arg" == "--lr-schedule="* ]]; then
        LR_SCHEDULE="${arg#*=}"
        break
    fi
done

# 输出将要执行的命令
echo "运行命令: python train_svhn_fixed.py --model $MODEL --epochs $EPOCHS --batch-size $BATCH_SIZE --lr $LR --gpu-id $GPU_ID $VAL --lr-schedule $LR_SCHEDULE"

# 执行训练
python train_svhn_fixed.py --model $MODEL --epochs $EPOCHS --batch-size $BATCH_SIZE --lr $LR --gpu-id $GPU_ID $VAL --lr-schedule $LR_SCHEDULE "$@" 