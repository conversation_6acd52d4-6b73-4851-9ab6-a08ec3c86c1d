import torch
import torchvision
import os
from torchvision.datasets import SVHN
import numpy as np

print("开始下载SVHN数据集...")

# 创建数据目录
os.makedirs('./data', exist_ok=True)

# 下载SVHN训练集和测试集
train_set = SVHN(root='./data', split='train', download=True)
test_set = SVHN(root='./data', split='test', download=True)

print(f"SVHN训练集大小: {len(train_set)}个样本")
print(f"SVHN测试集大小: {len(test_set)}个样本")
print("SVHN数据集已下载完成！") 