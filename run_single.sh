#!/bin/bash

# 检查参数
if [ "$#" -lt 2 ]; then
    echo "用法: $0 <dataset> <model> [epochs] [batch_size] [lr] [gpu_id]"
    echo "参数说明:"
    echo "  <dataset>: 必需，数据集名称 (cifar10, cifar100, svhn, fashion_mnist, caltech101)"
    echo "  <model>: 必需，模型名称 (ResNet18, ResNet50, DenseNet121, wide_resnet34_10)"
    echo "  [epochs]: 可选，训练轮数 (默认: cifar10/cifar100/svhn=200, fashion_mnist=100, caltech101=150)"
    echo "  [batch_size]: 可选，批次大小 (默认: cifar10/cifar100/svhn/fashion_mnist=128, caltech101=64)"
    echo "  [lr]: 可选，学习率 (默认: 0.1，caltech101=0.05)"
    echo "  [gpu_id]: 可选，指定使用的GPU ID (默认: 0)"
    echo ""
    echo "其他选项: 在命令后添加这些选项可启用特定功能"
    echo "  --val: 使用验证集进行训练评估"
    exit 1
fi

# 获取必需参数
DATASET=$1
MODEL=$2

# 根据数据集设置默认参数
if [ "$DATASET" == "fashion_mnist" ]; then
    DEFAULT_EPOCHS=100
    DEFAULT_BATCH_SIZE=128
    DEFAULT_LR=0.1
elif [ "$DATASET" == "caltech101" ]; then
    DEFAULT_EPOCHS=150
    DEFAULT_BATCH_SIZE=64
    DEFAULT_LR=0.05
else  # cifar10, cifar100, svhn
    DEFAULT_EPOCHS=200
    DEFAULT_BATCH_SIZE=128
    DEFAULT_LR=0.1
fi

# 获取可选参数或使用默认值
EPOCHS=${3:-$DEFAULT_EPOCHS}
BATCH_SIZE=${4:-$DEFAULT_BATCH_SIZE}
LR=${5:-$DEFAULT_LR}
GPU_ID=${6:-0}

# 设置只使用一个指定的GPU
export CUDA_VISIBLE_DEVICES=$GPU_ID

# 设置PGD攻击参数
EPSILON=8
PGD_ALPHA=2
ATTACK_ITERS=20

# 创建数据目录
mkdir -p ./data

# 创建带时间戳的输出目录名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="${DATASET}_${MODEL}_eps${EPSILON}_alpha${PGD_ALPHA}_steps${ATTACK_ITERS}_${TIMESTAMP}"

# 检查是否使用验证集
USE_VAL=""
for arg in "$@"; do
    if [ "$arg" == "--val" ]; then
        USE_VAL="--val"
        break
    fi
done

echo "==================================================="
echo "开始训练 - 数据集: $DATASET, 模型: $MODEL"
echo "参数设置:"
echo "  - 轮数: $EPOCHS"
echo "  - 批次大小: $BATCH_SIZE"
echo "  - 学习率: $LR"
echo "  - PGD参数: epsilon=$EPSILON/255, alpha=$PGD_ALPHA/255, steps=$ATTACK_ITERS"
echo "  - 使用GPU ID: $GPU_ID (只用一个GPU)"
if [ ! -z "$USE_VAL" ]; then
    echo "  - 使用验证集: 是"
else
    echo "  - 使用验证集: 否"
fi
echo "输出目录: $OUTPUT_DIR"
echo "==================================================="

# 根据数据集选择不同的训练脚本
if [ "$DATASET" == "fashion_mnist" ]; then #&& [ -f "train_fashion_mnist.py" ]; then
    python train_fashion_mnist.py \
      --model $MODEL \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --batch-size $BATCH_SIZE \
      --epochs $EPOCHS \
      --lr-max $LR \
      --lr-schedule piecewise \
      --norm l_inf \
      --fname $OUTPUT_DIR \
      --no-dp \
      $USE_VAL
elif [ "$DATASET" == "caltech101" ] ; then #&& [ -f "train_caltech101.py" ]; then
    python train_caltech101.py \
      --model $MODEL \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --batch-size $BATCH_SIZE \
      --epochs $EPOCHS \
      --lr-max $LR \
      --lr-schedule cosine \
      --norm l_inf \
      --fname $OUTPUT_DIR \
      --no-dp \
      $USE_VAL
elif [ "$DATASET" == "svhn" ] ; then #&& [ -f "train_svhn.py" ]; then
    # 使用专门的SVHN训练脚本
    python train_svhn.py \
      --model $MODEL \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --batch-size $BATCH_SIZE \
      --epochs $EPOCHS \
      --lr-max $LR \
      --lr-schedule piecewise \
      --norm l_inf \
      --fname $OUTPUT_DIR \
      --no-dp \
      $USE_VAL
elif [ "$DATASET" == "cifar100" ] ; then # && [ -f "train_cifar100.py" ]; then
    # 使用专门的CIFAR100训练脚本
    python train_cifar100.py \
      --model $MODEL \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --batch-size $BATCH_SIZE \
      --epochs $EPOCHS \
      --lr-max $LR \
      --lr-schedule piecewise \
      --norm l_inf \
      --fname $OUTPUT_DIR \
      --no-dp \
      $USE_VAL
else
    # 对于其他数据集或找不到专用脚本时使用通用脚本
    python train_cifar.py \
      --dataset $DATASET \
      --model $MODEL \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --batch-size $BATCH_SIZE \
      --epochs $EPOCHS \
      --lr-max $LR \
      --lr-schedule piecewise \
      --norm l_inf \
      --fname $OUTPUT_DIR \
      --no-dp \
      $USE_VAL
fi

echo "训练完成: $DATASET - $MODEL"
echo "结果保存在: $OUTPUT_DIR" 