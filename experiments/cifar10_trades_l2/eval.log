[2020/02/03 18:03:25] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=11, seed=0, val=False, width_factor=10)
[2020/02/03 18:04:18] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=11, seed=0, val=False, width_factor=10)
[2020/02/03 18:04:32] - Resuming at epoch 11
[2020/02/03 18:04:32] - [Evaluation mode]
[2020/02/03 18:04:32] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:08:04] - 11 	 0.9 	 	 211.1 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 2.0573 	 0.4633 	 2.1259 	 	 0.2283
[2020/02/03 18:11:11] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=21, seed=0, val=False, width_factor=10)
[2020/02/03 18:11:28] - Resuming at epoch 21
[2020/02/03 18:11:28] - [Evaluation mode]
[2020/02/03 18:11:28] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:13:49] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=21, seed=0, val=False, width_factor=10)
[2020/02/03 18:14:03] - Resuming at epoch 21
[2020/02/03 18:14:03] - [Evaluation mode]
[2020/02/03 18:14:03] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:17:38] - 21 	 1.2 	 	 213.0 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 2.1190 	 0.4960 	 2.1818 	 	 0.2380
[2020/02/03 18:17:54] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=22, seed=0, val=False, width_factor=10)
[2020/02/03 18:18:23] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=23, seed=0, val=False, width_factor=10)
[2020/02/03 18:18:53] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=24, seed=0, val=False, width_factor=10)
[2020/02/03 18:19:23] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=25, seed=0, val=False, width_factor=10)
[2020/02/03 18:19:53] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=26, seed=0, val=False, width_factor=10)
[2020/02/03 18:20:22] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=27, seed=0, val=False, width_factor=10)
[2020/02/03 18:20:52] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=28, seed=0, val=False, width_factor=10)
[2020/02/03 18:21:47] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=31, seed=0, val=False, width_factor=10)
[2020/02/03 18:22:03] - Resuming at epoch 31
[2020/02/03 18:22:03] - [Evaluation mode]
[2020/02/03 18:22:03] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:25:34] - 31 	 0.8 	 	 209.9 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.9917 	 0.6766 	 2.1216 	 	 0.3101
[2020/02/03 18:25:50] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=41, seed=0, val=False, width_factor=10)
[2020/02/03 18:26:05] - Resuming at epoch 41
[2020/02/03 18:26:05] - [Evaluation mode]
[2020/02/03 18:26:05] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:29:38] - 41 	 0.7 	 	 212.0 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 2.0113 	 0.6227 	 2.1477 	 	 0.2244
[2020/02/03 18:29:52] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=51, seed=0, val=False, width_factor=10)
[2020/02/03 18:30:08] - Resuming at epoch 51
[2020/02/03 18:30:08] - [Evaluation mode]
[2020/02/03 18:30:08] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:33:42] - 51 	 0.7 	 	 212.9 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 2.0932 	 0.5684 	 2.1979 	 	 0.1653
[2020/02/03 18:33:58] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=61, seed=0, val=False, width_factor=10)
[2020/02/03 18:34:14] - Resuming at epoch 61
[2020/02/03 18:34:14] - [Evaluation mode]
[2020/02/03 18:34:14] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:37:48] - 61 	 0.7 	 	 212.5 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.9109 	 0.6855 	 2.0779 	 	 0.1975
[2020/02/03 18:38:02] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=71, seed=0, val=False, width_factor=10)
[2020/02/03 18:38:19] - Resuming at epoch 71
[2020/02/03 18:38:19] - [Evaluation mode]
[2020/02/03 18:38:19] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:41:52] - 71 	 1.2 	 	 211.8 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.9462 	 0.6855 	 2.0835 	 	 0.1980
[2020/02/03 18:42:08] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=81, seed=0, val=False, width_factor=10)
[2020/02/03 18:42:22] - Resuming at epoch 81
[2020/02/03 18:42:22] - [Evaluation mode]
[2020/02/03 18:42:22] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:45:57] - 81 	 1.2 	 	 213.2 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 2.1484 	 0.3254 	 2.1640 	 	 0.2365
[2020/02/03 18:46:11] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=91, seed=0, val=False, width_factor=10)
[2020/02/03 18:46:28] - Resuming at epoch 91
[2020/02/03 18:46:28] - [Evaluation mode]
[2020/02/03 18:46:28] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:50:05] - 91 	 1.1 	 	 215.4 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.6731 	 0.7037 	 1.8488 	 	 0.2550
[2020/02/03 18:50:22] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=101, seed=0, val=False, width_factor=10)
[2020/02/03 18:50:38] - Resuming at epoch 101
[2020/02/03 18:50:38] - [Evaluation mode]
[2020/02/03 18:50:38] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 18:54:22] - 101 	 1.4 	 	 222.6 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.4271 	 0.7805 	 1.6553 	 	 0.3214
[2020/02/05 03:43:36] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=121, seed=0, val=False, width_factor=10)
[2020/02/05 03:44:54] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=111, seed=0, val=False, width_factor=10)
[2020/02/05 03:45:13] - Resuming at epoch 111
[2020/02/05 03:45:13] - [Evaluation mode]
[2020/02/05 03:45:13] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 03:52:50] - 111 	 0.7 	 	 456.1 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.4017 	 0.6680 	 1.6067 	 	 0.3027
[2020/02/05 03:53:10] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=121, seed=0, val=False, width_factor=10)
[2020/02/05 03:53:29] - Resuming at epoch 121
[2020/02/05 03:53:29] - [Evaluation mode]
[2020/02/05 03:53:29] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:01:06] - 121 	 1.2 	 	 455.7 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.5347 	 0.6727 	 1.6809 	 	 0.3688
[2020/02/05 04:01:27] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=131, seed=0, val=False, width_factor=10)
[2020/02/05 04:01:45] - Resuming at epoch 131
[2020/02/05 04:01:45] - [Evaluation mode]
[2020/02/05 04:01:45] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:09:23] - 131 	 1.2 	 	 457.5 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.5755 	 0.6279 	 1.7132 	 	 0.3354
[2020/02/05 04:09:46] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=141, seed=0, val=False, width_factor=10)
[2020/02/05 04:10:05] - Resuming at epoch 141
[2020/02/05 04:10:05] - [Evaluation mode]
[2020/02/05 04:10:05] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:17:42] - 141 	 1.4 	 	 455.4 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.8875 	 0.8433 	 2.0498 	 	 0.4644
[2020/02/05 04:18:03] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=151, seed=0, val=False, width_factor=10)
[2020/02/05 04:18:20] - Resuming at epoch 151
[2020/02/05 04:18:20] - [Evaluation mode]
[2020/02/05 04:18:20] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:25:59] - 151 	 1.6 	 	 456.6 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.8680 	 0.7797 	 2.0413 	 	 0.4124
[2020/02/05 04:26:20] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=161, seed=0, val=False, width_factor=10)
[2020/02/05 04:26:37] - Resuming at epoch 161
[2020/02/05 04:26:37] - [Evaluation mode]
[2020/02/05 04:26:37] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:34:15] - 161 	 1.5 	 	 456.4 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.7359 	 0.7652 	 1.8875 	 	 0.4544
[2020/02/05 04:34:35] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=171, seed=0, val=False, width_factor=10)
[2020/02/05 04:34:54] - Resuming at epoch 171
[2020/02/05 04:34:54] - [Evaluation mode]
[2020/02/05 04:34:54] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:42:29] - 171 	 1.3 	 	 454.1 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.7348 	 0.7008 	 1.8669 	 	 0.4353
[2020/02/05 04:42:54] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=181, seed=0, val=False, width_factor=10)
[2020/02/05 04:43:11] - Resuming at epoch 181
[2020/02/05 04:43:11] - [Evaluation mode]
[2020/02/05 04:43:11] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:50:44] - 181 	 1.6 	 	 451.3 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.7379 	 0.6803 	 1.8595 	 	 0.4256
[2020/02/05 04:51:05] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=191, seed=0, val=False, width_factor=10)
[2020/02/05 04:51:23] - Resuming at epoch 191
[2020/02/05 04:51:23] - [Evaluation mode]
[2020/02/05 04:51:23] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 04:59:00] - 191 	 1.5 	 	 454.7 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.7477 	 0.6798 	 1.8717 	 	 0.4201
[2020/02/05 04:59:22] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=201, seed=0, val=False, width_factor=10)
[2020/02/05 13:27:33] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=250, epsilon=128, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='/nethome/ericwong/TRADES/model-cifar-wideResNet-madry-l2', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='PreActResNet18', norm='l_2', pgd_alpha=15, restarts=1, resume=201, seed=0, val=False, width_factor=10)
[2020/02/05 13:27:49] - Resuming at epoch 201
[2020/02/05 13:27:49] - [Evaluation mode]
[2020/02/05 13:27:49] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/05 13:31:32] - 201 	 0.9 	 	 222.6 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 1.7529 	 0.6537 	 1.8639 	 	 0.4097
