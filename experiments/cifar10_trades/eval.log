[2020/02/03 04:33:19] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=200, epsilon=8, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='experiments/cifar10_trades/trades', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='WideResNet', norm='l_inf', pgd_alpha=2, restarts=1, resume=76, seed=0, val=False, width_factor=10)
[2020/02/03 04:33:31] - Resuming at epoch 76
[2020/02/03 04:33:31] - [Evaluation mode]
[2020/02/03 04:33:31] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 04:38:26] - 76 	 1.0 	 	 293.6 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 0.6297 	 0.8423 	 0.8940 	 	 0.5502
[2020/02/03 04:44:39] - Namespace(attack='pgd', attack_iters=10, batch_size=128, chkpt_iters=10, cutout=False, cutout_len=None, data_dir='../cifar-data', epochs=250, epsilon=8, eval=True, fgsm_alpha=1.25, fgsm_init='random', fname='experiments/cifar10_trades/trades', half=False, l1=0, l2=0, lr_drop_epoch=100, lr_max=0.1, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=None, model='WideResNet', norm='l_inf', pgd_alpha=2, restarts=1, resume=201, seed=0, val=False, width_factor=10)
[2020/02/03 04:44:48] - Resuming at epoch 201
[2020/02/03 04:44:48] - [Evaluation mode]
[2020/02/03 04:44:48] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
[2020/02/03 04:49:42] - 201 	 0.9 	 	 293.3 	 	 -1.0000 	 -1.0000 	 -1.0000 	 -1.0000 	 	 -1.0000 	 	 0.5455 	 0.8482 	 0.9918 	 	 0.4940
