waitGPU: Waiting for the following conditions, checking every 10 seconds. 
+ GPU id is [2]
waitGPU: Setting GPU to: [2]
Files already downloaded and verified
Files already downloaded and verified
Files already downloaded and verified
Train Epoch: 1 [0/50000 (0%)]	Loss: 2.407806
Train Epoch: 1 [12800/50000 (26%)]	Loss: 2.204236
Train Epoch: 1 [25600/50000 (51%)]	Loss: 2.115481
Train Epoch: 1 [38400/50000 (77%)]	Loss: 2.207681
================================================================
Training: Average loss: 2.0033, Accuracy: 13502/50000 (27%)
Test: Average loss: 1.9690, Accuracy: 2897/10000 (29%)
natural_err_total:  tensor(7103., device='cuda:0')
robust_err_total:  tensor(7912., device='cuda:0')
================================================================
Train Epoch: 2 [0/50000 (0%)]	Loss: 2.091496
Train Epoch: 2 [12800/50000 (26%)]	Loss: 2.034926
Train Epoch: 2 [25600/50000 (51%)]	Loss: 2.047217
Train Epoch: 2 [38400/50000 (77%)]	Loss: 1.983770
================================================================
Training: Average loss: 1.8307, Accuracy: 17595/50000 (35%)
Test: Average loss: 1.7737, Accuracy: 3754/10000 (38%)
natural_err_total:  tensor(6246., device='cuda:0')
robust_err_total:  tensor(7708., device='cuda:0')
================================================================
Train Epoch: 3 [0/50000 (0%)]	Loss: 1.812383
Train Epoch: 3 [12800/50000 (26%)]	Loss: 1.889482
Train Epoch: 3 [25600/50000 (51%)]	Loss: 1.854893
Train Epoch: 3 [38400/50000 (77%)]	Loss: 1.927665
================================================================
Training: Average loss: 1.7404, Accuracy: 19583/50000 (39%)
Test: Average loss: 1.6892, Accuracy: 4070/10000 (41%)
natural_err_total:  tensor(5930., device='cuda:0')
robust_err_total:  tensor(7455., device='cuda:0')
================================================================
Train Epoch: 4 [0/50000 (0%)]	Loss: 1.887354
Train Epoch: 4 [12800/50000 (26%)]	Loss: 1.829714
Train Epoch: 4 [25600/50000 (51%)]	Loss: 1.845212
Train Epoch: 4 [38400/50000 (77%)]	Loss: 1.907998
================================================================
Training: Average loss: 1.6585, Accuracy: 21734/50000 (43%)
Test: Average loss: 1.6153, Accuracy: 4408/10000 (44%)
natural_err_total:  tensor(5592., device='cuda:0')
robust_err_total:  tensor(7353., device='cuda:0')
================================================================
Train Epoch: 5 [0/50000 (0%)]	Loss: 1.781157
Train Epoch: 5 [12800/50000 (26%)]	Loss: 1.756513
Train Epoch: 5 [25600/50000 (51%)]	Loss: 1.832069
Train Epoch: 5 [38400/50000 (77%)]	Loss: 1.811357
================================================================
Training: Average loss: 1.5760, Accuracy: 23072/50000 (46%)
Test: Average loss: 1.5289, Accuracy: 4731/10000 (47%)
natural_err_total:  tensor(5269., device='cuda:0')
robust_err_total:  tensor(7076., device='cuda:0')
================================================================
Train Epoch: 6 [0/50000 (0%)]	Loss: 1.757023
Train Epoch: 6 [12800/50000 (26%)]	Loss: 1.835164
Train Epoch: 6 [25600/50000 (51%)]	Loss: 1.667718
Train Epoch: 6 [38400/50000 (77%)]	Loss: 1.636734
================================================================
Training: Average loss: 1.5350, Accuracy: 24431/50000 (49%)
Test: Average loss: 1.4886, Accuracy: 4983/10000 (50%)
natural_err_total:  tensor(5017., device='cuda:0')
robust_err_total:  tensor(7118., device='cuda:0')
================================================================
Train Epoch: 7 [0/50000 (0%)]	Loss: 1.769721
Train Epoch: 7 [12800/50000 (26%)]	Loss: 1.687833
Train Epoch: 7 [25600/50000 (51%)]	Loss: 1.707598
Train Epoch: 7 [38400/50000 (77%)]	Loss: 1.717185
================================================================
Training: Average loss: 1.4596, Accuracy: 26205/50000 (52%)
Test: Average loss: 1.4234, Accuracy: 5282/10000 (53%)
natural_err_total:  tensor(4718., device='cuda:0')
robust_err_total:  tensor(7028., device='cuda:0')
================================================================
Train Epoch: 8 [0/50000 (0%)]	Loss: 1.605898
Train Epoch: 8 [12800/50000 (26%)]	Loss: 1.594392
Train Epoch: 8 [25600/50000 (51%)]	Loss: 1.444531
Train Epoch: 8 [38400/50000 (77%)]	Loss: 1.643388
================================================================
Training: Average loss: 1.4170, Accuracy: 27506/50000 (55%)
Test: Average loss: 1.3757, Accuracy: 5613/10000 (56%)
natural_err_total:  tensor(4387., device='cuda:0')
robust_err_total:  tensor(6804., device='cuda:0')
================================================================
Train Epoch: 9 [0/50000 (0%)]	Loss: 1.610530
Train Epoch: 9 [12800/50000 (26%)]	Loss: 1.637121
Train Epoch: 9 [25600/50000 (51%)]	Loss: 1.782786
Train Epoch: 9 [38400/50000 (77%)]	Loss: 1.531189
================================================================
Training: Average loss: 1.4016, Accuracy: 27770/50000 (56%)
Test: Average loss: 1.3602, Accuracy: 5644/10000 (56%)
natural_err_total:  tensor(4356., device='cuda:0')
robust_err_total:  tensor(6670., device='cuda:0')
================================================================
Train Epoch: 10 [0/50000 (0%)]	Loss: 1.581672
Train Epoch: 10 [12800/50000 (26%)]	Loss: 1.564983
Train Epoch: 10 [25600/50000 (51%)]	Loss: 1.665847
Train Epoch: 10 [38400/50000 (77%)]	Loss: 1.615747
================================================================
Training: Average loss: 1.3346, Accuracy: 29857/50000 (60%)
Test: Average loss: 1.2975, Accuracy: 6003/10000 (60%)
natural_err_total:  tensor(3997., device='cuda:0')
robust_err_total:  tensor(6381., device='cuda:0')
================================================================
Train Epoch: 11 [0/50000 (0%)]	Loss: 1.698468
Train Epoch: 11 [12800/50000 (26%)]	Loss: 1.521009
Train Epoch: 11 [25600/50000 (51%)]	Loss: 1.610666
Train Epoch: 11 [38400/50000 (77%)]	Loss: 1.592378
================================================================
Training: Average loss: 1.3236, Accuracy: 30073/50000 (60%)
Test: Average loss: 1.3036, Accuracy: 5966/10000 (60%)
natural_err_total:  tensor(4034., device='cuda:0')
robust_err_total:  tensor(6586., device='cuda:0')
================================================================
Train Epoch: 12 [0/50000 (0%)]	Loss: 1.630695
Train Epoch: 12 [12800/50000 (26%)]	Loss: 1.554645
Train Epoch: 12 [25600/50000 (51%)]	Loss: 1.608784
Train Epoch: 12 [38400/50000 (77%)]	Loss: 1.409105
================================================================
Training: Average loss: 1.2892, Accuracy: 30209/50000 (60%)
Test: Average loss: 1.2700, Accuracy: 6044/10000 (60%)
natural_err_total:  tensor(3956., device='cuda:0')
robust_err_total:  tensor(6493., device='cuda:0')
================================================================
Train Epoch: 13 [0/50000 (0%)]	Loss: 1.519585
Train Epoch: 13 [12800/50000 (26%)]	Loss: 1.608632
Train Epoch: 13 [25600/50000 (51%)]	Loss: 1.440487
Train Epoch: 13 [38400/50000 (77%)]	Loss: 1.520336
================================================================
Training: Average loss: 1.2316, Accuracy: 32375/50000 (65%)
Test: Average loss: 1.2184, Accuracy: 6449/10000 (64%)
natural_err_total:  tensor(3551., device='cuda:0')
robust_err_total:  tensor(6306., device='cuda:0')
================================================================
Train Epoch: 14 [0/50000 (0%)]	Loss: 1.427700
Train Epoch: 14 [12800/50000 (26%)]	Loss: 1.401030
Train Epoch: 14 [25600/50000 (51%)]	Loss: 1.476349
Train Epoch: 14 [38400/50000 (77%)]	Loss: 1.580443
================================================================
Training: Average loss: 1.2052, Accuracy: 32913/50000 (66%)
Test: Average loss: 1.1783, Accuracy: 6565/10000 (66%)
natural_err_total:  tensor(3435., device='cuda:0')
robust_err_total:  tensor(6154., device='cuda:0')
================================================================
Train Epoch: 15 [0/50000 (0%)]	Loss: 1.343052
Train Epoch: 15 [12800/50000 (26%)]	Loss: 1.583831
Train Epoch: 15 [25600/50000 (51%)]	Loss: 1.514916
Train Epoch: 15 [38400/50000 (77%)]	Loss: 1.443197
================================================================
Training: Average loss: 1.1807, Accuracy: 34025/50000 (68%)
Test: Average loss: 1.1757, Accuracy: 6692/10000 (67%)
natural_err_total:  tensor(3308., device='cuda:0')
robust_err_total:  tensor(6267., device='cuda:0')
================================================================
Train Epoch: 16 [0/50000 (0%)]	Loss: 1.390829
Train Epoch: 16 [12800/50000 (26%)]	Loss: 1.569302
Train Epoch: 16 [25600/50000 (51%)]	Loss: 1.449732
Train Epoch: 16 [38400/50000 (77%)]	Loss: 1.329086
================================================================
Training: Average loss: 1.1662, Accuracy: 34149/50000 (68%)
Test: Average loss: 1.1650, Accuracy: 6712/10000 (67%)
natural_err_total:  tensor(3288., device='cuda:0')
robust_err_total:  tensor(6023., device='cuda:0')
================================================================
Train Epoch: 17 [0/50000 (0%)]	Loss: 1.359106
Train Epoch: 17 [12800/50000 (26%)]	Loss: 1.391997
Train Epoch: 17 [25600/50000 (51%)]	Loss: 1.382378
Train Epoch: 17 [38400/50000 (77%)]	Loss: 1.440406
================================================================
Training: Average loss: 1.1337, Accuracy: 34946/50000 (70%)
Test: Average loss: 1.1253, Accuracy: 6862/10000 (69%)
natural_err_total:  tensor(3138., device='cuda:0')
robust_err_total:  tensor(5960., device='cuda:0')
================================================================
Train Epoch: 18 [0/50000 (0%)]	Loss: 1.437424
Train Epoch: 18 [12800/50000 (26%)]	Loss: 1.411972
Train Epoch: 18 [25600/50000 (51%)]	Loss: 1.344632
Train Epoch: 18 [38400/50000 (77%)]	Loss: 1.484950
================================================================
Training: Average loss: 1.0996, Accuracy: 35090/50000 (70%)
Test: Average loss: 1.0999, Accuracy: 6862/10000 (69%)
natural_err_total:  tensor(3138., device='cuda:0')
robust_err_total:  tensor(5886., device='cuda:0')
================================================================
Train Epoch: 19 [0/50000 (0%)]	Loss: 1.422848
Train Epoch: 19 [12800/50000 (26%)]	Loss: 1.477339
Train Epoch: 19 [25600/50000 (51%)]	Loss: 1.378969
Train Epoch: 19 [38400/50000 (77%)]	Loss: 1.354545
================================================================
Training: Average loss: 1.0817, Accuracy: 35593/50000 (71%)
Test: Average loss: 1.0796, Accuracy: 6942/10000 (69%)
natural_err_total:  tensor(3058., device='cuda:0')
robust_err_total:  tensor(5875., device='cuda:0')
================================================================
Train Epoch: 20 [0/50000 (0%)]	Loss: 1.405857
Train Epoch: 20 [12800/50000 (26%)]	Loss: 1.443625
Train Epoch: 20 [25600/50000 (51%)]	Loss: 1.484882
Train Epoch: 20 [38400/50000 (77%)]	Loss: 1.274029
================================================================
Training: Average loss: 1.1085, Accuracy: 34982/50000 (70%)
Test: Average loss: 1.1127, Accuracy: 6817/10000 (68%)
natural_err_total:  tensor(3183., device='cuda:0')
robust_err_total:  tensor(5976., device='cuda:0')
================================================================
Train Epoch: 21 [0/50000 (0%)]	Loss: 1.337289
Train Epoch: 21 [12800/50000 (26%)]	Loss: 1.375320
Train Epoch: 21 [25600/50000 (51%)]	Loss: 1.343754
Train Epoch: 21 [38400/50000 (77%)]	Loss: 1.448607
================================================================
Training: Average loss: 1.0882, Accuracy: 35693/50000 (71%)
Test: Average loss: 1.0972, Accuracy: 7007/10000 (70%)
natural_err_total:  tensor(2993., device='cuda:0')
robust_err_total:  tensor(5699., device='cuda:0')
================================================================
Train Epoch: 22 [0/50000 (0%)]	Loss: 1.352378
Train Epoch: 22 [12800/50000 (26%)]	Loss: 1.356349
Train Epoch: 22 [25600/50000 (51%)]	Loss: 1.256574
Train Epoch: 22 [38400/50000 (77%)]	Loss: 1.301103
================================================================
Training: Average loss: 1.1091, Accuracy: 35660/50000 (71%)
Test: Average loss: 1.1062, Accuracy: 7003/10000 (70%)
natural_err_total:  tensor(2997., device='cuda:0')
robust_err_total:  tensor(5733., device='cuda:0')
================================================================
Train Epoch: 23 [0/50000 (0%)]	Loss: 1.369277
Train Epoch: 23 [12800/50000 (26%)]	Loss: 1.322867
Train Epoch: 23 [25600/50000 (51%)]	Loss: 1.340458
Train Epoch: 23 [38400/50000 (77%)]	Loss: 1.304790
================================================================
Training: Average loss: 1.0615, Accuracy: 36505/50000 (73%)
Test: Average loss: 1.0778, Accuracy: 7085/10000 (71%)
natural_err_total:  tensor(2915., device='cuda:0')
robust_err_total:  tensor(5827., device='cuda:0')
================================================================
Train Epoch: 24 [0/50000 (0%)]	Loss: 1.364450
Train Epoch: 24 [12800/50000 (26%)]	Loss: 1.317052
Train Epoch: 24 [25600/50000 (51%)]	Loss: 1.390815
Train Epoch: 24 [38400/50000 (77%)]	Loss: 1.386818
================================================================
Training: Average loss: 1.0884, Accuracy: 35616/50000 (71%)
Test: Average loss: 1.1027, Accuracy: 6904/10000 (69%)
natural_err_total:  tensor(3096., device='cuda:0')
robust_err_total:  tensor(5736., device='cuda:0')
================================================================
Train Epoch: 25 [0/50000 (0%)]	Loss: 1.482373
Train Epoch: 25 [12800/50000 (26%)]	Loss: 1.322937
Train Epoch: 25 [25600/50000 (51%)]	Loss: 1.364375
Train Epoch: 25 [38400/50000 (77%)]	Loss: 1.307760
================================================================
Training: Average loss: 1.0531, Accuracy: 37028/50000 (74%)
Test: Average loss: 1.0732, Accuracy: 7133/10000 (71%)
natural_err_total:  tensor(2867., device='cuda:0')
robust_err_total:  tensor(5698., device='cuda:0')
================================================================
Train Epoch: 26 [0/50000 (0%)]	Loss: 1.327507
Train Epoch: 26 [12800/50000 (26%)]	Loss: 1.287563
Train Epoch: 26 [25600/50000 (51%)]	Loss: 1.281767
Train Epoch: 26 [38400/50000 (77%)]	Loss: 1.430194
================================================================
Training: Average loss: 1.0123, Accuracy: 37751/50000 (76%)
Test: Average loss: 1.0391, Accuracy: 7266/10000 (73%)
natural_err_total:  tensor(2734., device='cuda:0')
robust_err_total:  tensor(5517., device='cuda:0')
================================================================
Train Epoch: 27 [0/50000 (0%)]	Loss: 1.325157
Train Epoch: 27 [12800/50000 (26%)]	Loss: 1.294339
Train Epoch: 27 [25600/50000 (51%)]	Loss: 1.464033
Train Epoch: 27 [38400/50000 (77%)]	Loss: 1.214072
================================================================
Training: Average loss: 1.0523, Accuracy: 37029/50000 (74%)
Test: Average loss: 1.0765, Accuracy: 7107/10000 (71%)
natural_err_total:  tensor(2893., device='cuda:0')
robust_err_total:  tensor(5805., device='cuda:0')
================================================================
Train Epoch: 28 [0/50000 (0%)]	Loss: 1.438519
Train Epoch: 28 [12800/50000 (26%)]	Loss: 1.283221
Train Epoch: 28 [25600/50000 (51%)]	Loss: 1.255865
Train Epoch: 28 [38400/50000 (77%)]	Loss: 1.326645
================================================================
Training: Average loss: 0.9815, Accuracy: 37427/50000 (75%)
Test: Average loss: 1.0077, Accuracy: 7251/10000 (73%)
natural_err_total:  tensor(2749., device='cuda:0')
robust_err_total:  tensor(5541., device='cuda:0')
================================================================
Train Epoch: 29 [0/50000 (0%)]	Loss: 1.268482
Train Epoch: 29 [12800/50000 (26%)]	Loss: 1.348137
Train Epoch: 29 [25600/50000 (51%)]	Loss: 1.311233
Train Epoch: 29 [38400/50000 (77%)]	Loss: 1.230368
================================================================
Training: Average loss: 1.0212, Accuracy: 36374/50000 (73%)
Test: Average loss: 1.0480, Accuracy: 7004/10000 (70%)
natural_err_total:  tensor(2996., device='cuda:0')
robust_err_total:  tensor(5552., device='cuda:0')
================================================================
Train Epoch: 30 [0/50000 (0%)]	Loss: 1.332711
Train Epoch: 30 [12800/50000 (26%)]	Loss: 1.264412
Train Epoch: 30 [25600/50000 (51%)]	Loss: 1.208495
Train Epoch: 30 [38400/50000 (77%)]	Loss: 1.373540
================================================================
Training: Average loss: 1.0023, Accuracy: 37025/50000 (74%)
Test: Average loss: 1.0299, Accuracy: 7110/10000 (71%)
natural_err_total:  tensor(2890., device='cuda:0')
robust_err_total:  tensor(5679., device='cuda:0')
================================================================
Train Epoch: 31 [0/50000 (0%)]	Loss: 1.300860
Train Epoch: 31 [12800/50000 (26%)]	Loss: 1.338507
Train Epoch: 31 [25600/50000 (51%)]	Loss: 1.225179
Train Epoch: 31 [38400/50000 (77%)]	Loss: 1.306424
================================================================
Training: Average loss: 1.0059, Accuracy: 38234/50000 (76%)
Test: Average loss: 1.0427, Accuracy: 7236/10000 (72%)
natural_err_total:  tensor(2764., device='cuda:0')
robust_err_total:  tensor(5608., device='cuda:0')
================================================================
Train Epoch: 32 [0/50000 (0%)]	Loss: 1.153448
Train Epoch: 32 [12800/50000 (26%)]	Loss: 1.308138
Train Epoch: 32 [25600/50000 (51%)]	Loss: 1.245310
Train Epoch: 32 [38400/50000 (77%)]	Loss: 1.369810
================================================================
Training: Average loss: 1.0239, Accuracy: 37845/50000 (76%)
Test: Average loss: 1.0485, Accuracy: 7283/10000 (73%)
natural_err_total:  tensor(2717., device='cuda:0')
robust_err_total:  tensor(5456., device='cuda:0')
================================================================
Train Epoch: 33 [0/50000 (0%)]	Loss: 1.201072
Train Epoch: 33 [12800/50000 (26%)]	Loss: 1.305269
Train Epoch: 33 [25600/50000 (51%)]	Loss: 1.275059
Train Epoch: 33 [38400/50000 (77%)]	Loss: 1.345390
================================================================
Training: Average loss: 0.9644, Accuracy: 39134/50000 (78%)
Test: Average loss: 0.9968, Accuracy: 7450/10000 (74%)
natural_err_total:  tensor(2550., device='cuda:0')
robust_err_total:  tensor(5493., device='cuda:0')
================================================================
Train Epoch: 34 [0/50000 (0%)]	Loss: 1.224709
Train Epoch: 34 [12800/50000 (26%)]	Loss: 1.373949
Train Epoch: 34 [25600/50000 (51%)]	Loss: 1.229109
Train Epoch: 34 [38400/50000 (77%)]	Loss: 1.296359
================================================================
Training: Average loss: 0.9873, Accuracy: 39046/50000 (78%)
Test: Average loss: 1.0261, Accuracy: 7451/10000 (75%)
natural_err_total:  tensor(2549., device='cuda:0')
robust_err_total:  tensor(5642., device='cuda:0')
================================================================
Train Epoch: 35 [0/50000 (0%)]	Loss: 1.241019
Train Epoch: 35 [12800/50000 (26%)]	Loss: 1.258792
Train Epoch: 35 [25600/50000 (51%)]	Loss: 1.257934
Train Epoch: 35 [38400/50000 (77%)]	Loss: 1.314659
================================================================
Training: Average loss: 0.9568, Accuracy: 38664/50000 (77%)
Test: Average loss: 0.9995, Accuracy: 7352/10000 (74%)
natural_err_total:  tensor(2648., device='cuda:0')
robust_err_total:  tensor(5554., device='cuda:0')
================================================================
Train Epoch: 36 [0/50000 (0%)]	Loss: 1.294365
Train Epoch: 36 [12800/50000 (26%)]	Loss: 1.129618
Train Epoch: 36 [25600/50000 (51%)]	Loss: 1.221257
Train Epoch: 36 [38400/50000 (77%)]	Loss: 1.245096
================================================================
Training: Average loss: 0.9728, Accuracy: 38842/50000 (78%)
Test: Average loss: 1.0184, Accuracy: 7464/10000 (75%)
natural_err_total:  tensor(2536., device='cuda:0')
robust_err_total:  tensor(5438., device='cuda:0')
================================================================
Train Epoch: 37 [0/50000 (0%)]	Loss: 1.294308
Train Epoch: 37 [12800/50000 (26%)]	Loss: 1.266682
Train Epoch: 37 [25600/50000 (51%)]	Loss: 1.255042
Train Epoch: 37 [38400/50000 (77%)]	Loss: 1.224397
================================================================
Training: Average loss: 0.9567, Accuracy: 37440/50000 (75%)
Test: Average loss: 0.9976, Accuracy: 7184/10000 (72%)
natural_err_total:  tensor(2816., device='cuda:0')
robust_err_total:  tensor(5476., device='cuda:0')
================================================================
Train Epoch: 38 [0/50000 (0%)]	Loss: 1.106720
Train Epoch: 38 [12800/50000 (26%)]	Loss: 1.225440
Train Epoch: 38 [25600/50000 (51%)]	Loss: 1.182493
Train Epoch: 38 [38400/50000 (77%)]	Loss: 1.205941
================================================================
Training: Average loss: 0.9155, Accuracy: 39167/50000 (78%)
Test: Average loss: 0.9548, Accuracy: 7432/10000 (74%)
natural_err_total:  tensor(2568., device='cuda:0')
robust_err_total:  tensor(5321., device='cuda:0')
================================================================
Train Epoch: 39 [0/50000 (0%)]	Loss: 1.372466
Train Epoch: 39 [12800/50000 (26%)]	Loss: 1.340366
Train Epoch: 39 [25600/50000 (51%)]	Loss: 1.248507
Train Epoch: 39 [38400/50000 (77%)]	Loss: 1.170350
================================================================
Training: Average loss: 0.9372, Accuracy: 38666/50000 (77%)
Test: Average loss: 0.9759, Accuracy: 7396/10000 (74%)
natural_err_total:  tensor(2604., device='cuda:0')
robust_err_total:  tensor(5316., device='cuda:0')
================================================================
Train Epoch: 40 [0/50000 (0%)]	Loss: 1.093075
Train Epoch: 40 [12800/50000 (26%)]	Loss: 1.222679
Train Epoch: 40 [25600/50000 (51%)]	Loss: 1.229600
Train Epoch: 40 [38400/50000 (77%)]	Loss: 1.351523
================================================================
Training: Average loss: 0.9309, Accuracy: 39488/50000 (79%)
Test: Average loss: 0.9811, Accuracy: 7406/10000 (74%)
natural_err_total:  tensor(2594., device='cuda:0')
robust_err_total:  tensor(5375., device='cuda:0')
================================================================
Train Epoch: 41 [0/50000 (0%)]	Loss: 1.331054
Train Epoch: 41 [12800/50000 (26%)]	Loss: 1.308933
Train Epoch: 41 [25600/50000 (51%)]	Loss: 1.311845
Train Epoch: 41 [38400/50000 (77%)]	Loss: 1.210463
================================================================
Training: Average loss: 0.9470, Accuracy: 38959/50000 (78%)
Test: Average loss: 0.9805, Accuracy: 7403/10000 (74%)
natural_err_total:  tensor(2597., device='cuda:0')
robust_err_total:  tensor(5285., device='cuda:0')
================================================================
Train Epoch: 42 [0/50000 (0%)]	Loss: 1.154967
Train Epoch: 42 [12800/50000 (26%)]	Loss: 1.282393
Train Epoch: 42 [25600/50000 (51%)]	Loss: 1.205000
Train Epoch: 42 [38400/50000 (77%)]	Loss: 1.229352
================================================================
Training: Average loss: 0.8945, Accuracy: 39979/50000 (80%)
Test: Average loss: 0.9468, Accuracy: 7556/10000 (76%)
natural_err_total:  tensor(2444., device='cuda:0')
robust_err_total:  tensor(5307., device='cuda:0')
================================================================
Train Epoch: 43 [0/50000 (0%)]	Loss: 1.094944
Train Epoch: 43 [12800/50000 (26%)]	Loss: 1.251493
Train Epoch: 43 [25600/50000 (51%)]	Loss: 1.217842
Train Epoch: 43 [38400/50000 (77%)]	Loss: 1.221450
================================================================
Training: Average loss: 0.9398, Accuracy: 39522/50000 (79%)
Test: Average loss: 0.9832, Accuracy: 7473/10000 (75%)
natural_err_total:  tensor(2527., device='cuda:0')
robust_err_total:  tensor(5380., device='cuda:0')
================================================================
Train Epoch: 44 [0/50000 (0%)]	Loss: 1.216540
Train Epoch: 44 [12800/50000 (26%)]	Loss: 1.268103
Train Epoch: 44 [25600/50000 (51%)]	Loss: 1.322113
Train Epoch: 44 [38400/50000 (77%)]	Loss: 1.240298
================================================================
Training: Average loss: 0.8949, Accuracy: 39890/50000 (80%)
Test: Average loss: 0.9567, Accuracy: 7530/10000 (75%)
natural_err_total:  tensor(2470., device='cuda:0')
robust_err_total:  tensor(5400., device='cuda:0')
================================================================
Train Epoch: 45 [0/50000 (0%)]	Loss: 1.277370
Train Epoch: 45 [12800/50000 (26%)]	Loss: 1.215706
Train Epoch: 45 [25600/50000 (51%)]	Loss: 1.241971
Train Epoch: 45 [38400/50000 (77%)]	Loss: 1.241559
================================================================
Training: Average loss: 0.9532, Accuracy: 38399/50000 (77%)
Test: Average loss: 0.9991, Accuracy: 7299/10000 (73%)
natural_err_total:  tensor(2701., device='cuda:0')
robust_err_total:  tensor(5406., device='cuda:0')
================================================================
Train Epoch: 46 [0/50000 (0%)]	Loss: 1.300229
Train Epoch: 46 [12800/50000 (26%)]	Loss: 1.131508
Train Epoch: 46 [25600/50000 (51%)]	Loss: 1.256821
Train Epoch: 46 [38400/50000 (77%)]	Loss: 1.202686
================================================================
Training: Average loss: 0.8853, Accuracy: 39967/50000 (80%)
Test: Average loss: 0.9508, Accuracy: 7526/10000 (75%)
natural_err_total:  tensor(2474., device='cuda:0')
robust_err_total:  tensor(5341., device='cuda:0')
================================================================
Train Epoch: 47 [0/50000 (0%)]	Loss: 1.233994
Train Epoch: 47 [12800/50000 (26%)]	Loss: 1.135504
Train Epoch: 47 [25600/50000 (51%)]	Loss: 1.253796
Train Epoch: 47 [38400/50000 (77%)]	Loss: 1.253126
================================================================
Training: Average loss: 0.8945, Accuracy: 39149/50000 (78%)
Test: Average loss: 0.9646, Accuracy: 7424/10000 (74%)
natural_err_total:  tensor(2576., device='cuda:0')
robust_err_total:  tensor(5369., device='cuda:0')
================================================================
Train Epoch: 48 [0/50000 (0%)]	Loss: 1.148569
Train Epoch: 48 [12800/50000 (26%)]	Loss: 1.195810
Train Epoch: 48 [25600/50000 (51%)]	Loss: 1.162686
Train Epoch: 48 [38400/50000 (77%)]	Loss: 1.142872
================================================================
Training: Average loss: 0.9144, Accuracy: 39129/50000 (78%)
Test: Average loss: 0.9675, Accuracy: 7433/10000 (74%)
natural_err_total:  tensor(2567., device='cuda:0')
robust_err_total:  tensor(5415., device='cuda:0')
================================================================
Train Epoch: 49 [0/50000 (0%)]	Loss: 1.164231
Train Epoch: 49 [12800/50000 (26%)]	Loss: 1.175026
Train Epoch: 49 [25600/50000 (51%)]	Loss: 1.369657
Train Epoch: 49 [38400/50000 (77%)]	Loss: 1.194450
================================================================
Training: Average loss: 0.9139, Accuracy: 40171/50000 (80%)
Test: Average loss: 0.9622, Accuracy: 7545/10000 (75%)
natural_err_total:  tensor(2455., device='cuda:0')
robust_err_total:  tensor(5269., device='cuda:0')
================================================================
Train Epoch: 50 [0/50000 (0%)]	Loss: 1.246618
Train Epoch: 50 [12800/50000 (26%)]	Loss: 1.181062
Train Epoch: 50 [25600/50000 (51%)]	Loss: 1.249624
Train Epoch: 50 [38400/50000 (77%)]	Loss: 1.324791
================================================================
Training: Average loss: 0.9015, Accuracy: 40637/50000 (81%)
Test: Average loss: 0.9503, Accuracy: 7668/10000 (77%)
natural_err_total:  tensor(2332., device='cuda:0')
robust_err_total:  tensor(5224., device='cuda:0')
================================================================
Train Epoch: 51 [0/50000 (0%)]	Loss: 1.193414
Train Epoch: 51 [12800/50000 (26%)]	Loss: 1.269255
Train Epoch: 51 [25600/50000 (51%)]	Loss: 1.205580
Train Epoch: 51 [38400/50000 (77%)]	Loss: 1.260967
================================================================
Training: Average loss: 0.9197, Accuracy: 39861/50000 (80%)
Test: Average loss: 0.9812, Accuracy: 7463/10000 (75%)
natural_err_total:  tensor(2537., device='cuda:0')
robust_err_total:  tensor(5229., device='cuda:0')
================================================================
Train Epoch: 52 [0/50000 (0%)]	Loss: 1.268617
Train Epoch: 52 [12800/50000 (26%)]	Loss: 1.266278
Train Epoch: 52 [25600/50000 (51%)]	Loss: 1.230867
Train Epoch: 52 [38400/50000 (77%)]	Loss: 1.214907
================================================================
Training: Average loss: 0.8612, Accuracy: 40574/50000 (81%)
Test: Average loss: 0.9280, Accuracy: 7635/10000 (76%)
natural_err_total:  tensor(2365., device='cuda:0')
robust_err_total:  tensor(5214., device='cuda:0')
================================================================
Train Epoch: 53 [0/50000 (0%)]	Loss: 1.208905
Train Epoch: 53 [12800/50000 (26%)]	Loss: 1.255844
Train Epoch: 53 [25600/50000 (51%)]	Loss: 1.231956
Train Epoch: 53 [38400/50000 (77%)]	Loss: 1.250933
================================================================
Training: Average loss: 0.8955, Accuracy: 39807/50000 (80%)
Test: Average loss: 0.9469, Accuracy: 7531/10000 (75%)
natural_err_total:  tensor(2469., device='cuda:0')
robust_err_total:  tensor(5248., device='cuda:0')
================================================================
Train Epoch: 54 [0/50000 (0%)]	Loss: 1.211313
Train Epoch: 54 [12800/50000 (26%)]	Loss: 1.274479
Train Epoch: 54 [25600/50000 (51%)]	Loss: 1.200692
Train Epoch: 54 [38400/50000 (77%)]	Loss: 1.137881
================================================================
Training: Average loss: 0.8871, Accuracy: 40634/50000 (81%)
Test: Average loss: 0.9510, Accuracy: 7579/10000 (76%)
natural_err_total:  tensor(2421., device='cuda:0')
robust_err_total:  tensor(5299., device='cuda:0')
================================================================
Train Epoch: 55 [0/50000 (0%)]	Loss: 1.143372
Train Epoch: 55 [12800/50000 (26%)]	Loss: 1.451073
Train Epoch: 55 [25600/50000 (51%)]	Loss: 1.272505
Train Epoch: 55 [38400/50000 (77%)]	Loss: 1.244574
================================================================
Training: Average loss: 0.8899, Accuracy: 40488/50000 (81%)
Test: Average loss: 0.9480, Accuracy: 7569/10000 (76%)
natural_err_total:  tensor(2431., device='cuda:0')
robust_err_total:  tensor(5210., device='cuda:0')
================================================================
Train Epoch: 56 [0/50000 (0%)]	Loss: 1.175530
Train Epoch: 56 [12800/50000 (26%)]	Loss: 1.181396
Train Epoch: 56 [25600/50000 (51%)]	Loss: 1.154700
Train Epoch: 56 [38400/50000 (77%)]	Loss: 1.294566
================================================================
Training: Average loss: 0.8980, Accuracy: 40281/50000 (81%)
Test: Average loss: 0.9545, Accuracy: 7523/10000 (75%)
natural_err_total:  tensor(2477., device='cuda:0')
robust_err_total:  tensor(5264., device='cuda:0')
================================================================
Train Epoch: 57 [0/50000 (0%)]	Loss: 1.221931
Train Epoch: 57 [12800/50000 (26%)]	Loss: 1.196262
Train Epoch: 57 [25600/50000 (51%)]	Loss: 1.084096
Train Epoch: 57 [38400/50000 (77%)]	Loss: 1.182069
================================================================
Training: Average loss: 0.8888, Accuracy: 40789/50000 (82%)
Test: Average loss: 0.9512, Accuracy: 7641/10000 (76%)
natural_err_total:  tensor(2359., device='cuda:0')
robust_err_total:  tensor(5402., device='cuda:0')
================================================================
Train Epoch: 58 [0/50000 (0%)]	Loss: 1.302312
Train Epoch: 58 [12800/50000 (26%)]	Loss: 1.216264
Train Epoch: 58 [25600/50000 (51%)]	Loss: 1.145732
Train Epoch: 58 [38400/50000 (77%)]	Loss: 1.162045
================================================================
Training: Average loss: 0.8677, Accuracy: 40566/50000 (81%)
Test: Average loss: 0.9397, Accuracy: 7559/10000 (76%)
natural_err_total:  tensor(2441., device='cuda:0')
robust_err_total:  tensor(5302., device='cuda:0')
================================================================
Train Epoch: 59 [0/50000 (0%)]	Loss: 1.229124
Train Epoch: 59 [12800/50000 (26%)]	Loss: 1.217250
Train Epoch: 59 [25600/50000 (51%)]	Loss: 1.280546
Train Epoch: 59 [38400/50000 (77%)]	Loss: 1.220056
================================================================
Training: Average loss: 0.8706, Accuracy: 39849/50000 (80%)
Test: Average loss: 0.9436, Accuracy: 7476/10000 (75%)
natural_err_total:  tensor(2524., device='cuda:0')
robust_err_total:  tensor(5222., device='cuda:0')
================================================================
Train Epoch: 60 [0/50000 (0%)]	Loss: 1.162385
Train Epoch: 60 [12800/50000 (26%)]	Loss: 1.233994
Train Epoch: 60 [25600/50000 (51%)]	Loss: 1.218637
Train Epoch: 60 [38400/50000 (77%)]	Loss: 1.179744
================================================================
Training: Average loss: 0.8594, Accuracy: 40672/50000 (81%)
Test: Average loss: 0.9289, Accuracy: 7545/10000 (75%)
natural_err_total:  tensor(2455., device='cuda:0')
robust_err_total:  tensor(5241., device='cuda:0')
================================================================
Train Epoch: 61 [0/50000 (0%)]	Loss: 1.192671
Train Epoch: 61 [12800/50000 (26%)]	Loss: 1.201031
Train Epoch: 61 [25600/50000 (51%)]	Loss: 1.185157
Train Epoch: 61 [38400/50000 (77%)]	Loss: 1.235806
================================================================
Training: Average loss: 0.8599, Accuracy: 40687/50000 (81%)
Test: Average loss: 0.9422, Accuracy: 7643/10000 (76%)
natural_err_total:  tensor(2357., device='cuda:0')
robust_err_total:  tensor(5284., device='cuda:0')
================================================================
Train Epoch: 62 [0/50000 (0%)]	Loss: 1.155358
Train Epoch: 62 [12800/50000 (26%)]	Loss: 1.208670
Train Epoch: 62 [25600/50000 (51%)]	Loss: 1.351253
Train Epoch: 62 [38400/50000 (77%)]	Loss: 1.177824
================================================================
Training: Average loss: 0.8568, Accuracy: 41232/50000 (82%)
Test: Average loss: 0.9302, Accuracy: 7670/10000 (77%)
natural_err_total:  tensor(2330., device='cuda:0')
robust_err_total:  tensor(5234., device='cuda:0')
================================================================
Train Epoch: 63 [0/50000 (0%)]	Loss: 1.121865
Train Epoch: 63 [12800/50000 (26%)]	Loss: 1.214280
Train Epoch: 63 [25600/50000 (51%)]	Loss: 1.120791
Train Epoch: 63 [38400/50000 (77%)]	Loss: 1.147140
================================================================
Training: Average loss: 0.8713, Accuracy: 41453/50000 (83%)
Test: Average loss: 0.9398, Accuracy: 7720/10000 (77%)
natural_err_total:  tensor(2280., device='cuda:0')
robust_err_total:  tensor(5176., device='cuda:0')
================================================================
Train Epoch: 64 [0/50000 (0%)]	Loss: 1.217842
Train Epoch: 64 [12800/50000 (26%)]	Loss: 1.298861
Train Epoch: 64 [25600/50000 (51%)]	Loss: 1.178278
Train Epoch: 64 [38400/50000 (77%)]	Loss: 1.155087
================================================================
Training: Average loss: 0.8644, Accuracy: 40910/50000 (82%)
Test: Average loss: 0.9420, Accuracy: 7583/10000 (76%)
natural_err_total:  tensor(2417., device='cuda:0')
robust_err_total:  tensor(5245., device='cuda:0')
================================================================
Train Epoch: 65 [0/50000 (0%)]	Loss: 1.154284
Train Epoch: 65 [12800/50000 (26%)]	Loss: 1.083337
Train Epoch: 65 [25600/50000 (51%)]	Loss: 1.136841
Train Epoch: 65 [38400/50000 (77%)]	Loss: 1.216923
================================================================
Training: Average loss: 0.8633, Accuracy: 41012/50000 (82%)
Test: Average loss: 0.9261, Accuracy: 7654/10000 (77%)
natural_err_total:  tensor(2346., device='cuda:0')
robust_err_total:  tensor(5168., device='cuda:0')
================================================================
Train Epoch: 66 [0/50000 (0%)]	Loss: 1.227459
Train Epoch: 66 [12800/50000 (26%)]	Loss: 1.216782
Train Epoch: 66 [25600/50000 (51%)]	Loss: 1.198848
Train Epoch: 66 [38400/50000 (77%)]	Loss: 1.178452
================================================================
Training: Average loss: 0.8527, Accuracy: 40982/50000 (82%)
Test: Average loss: 0.9178, Accuracy: 7713/10000 (77%)
natural_err_total:  tensor(2287., device='cuda:0')
robust_err_total:  tensor(5164., device='cuda:0')
================================================================
Train Epoch: 67 [0/50000 (0%)]	Loss: 1.201793
Train Epoch: 67 [12800/50000 (26%)]	Loss: 1.224548
Train Epoch: 67 [25600/50000 (51%)]	Loss: 1.179065
Train Epoch: 67 [38400/50000 (77%)]	Loss: 1.219736
================================================================
Training: Average loss: 0.8473, Accuracy: 40369/50000 (81%)
Test: Average loss: 0.9243, Accuracy: 7567/10000 (76%)
natural_err_total:  tensor(2433., device='cuda:0')
robust_err_total:  tensor(5214., device='cuda:0')
================================================================
Train Epoch: 68 [0/50000 (0%)]	Loss: 1.183187
Train Epoch: 68 [12800/50000 (26%)]	Loss: 1.185903
Train Epoch: 68 [25600/50000 (51%)]	Loss: 1.208468
Train Epoch: 68 [38400/50000 (77%)]	Loss: 1.204701
================================================================
Training: Average loss: 0.8919, Accuracy: 41020/50000 (82%)
Test: Average loss: 0.9682, Accuracy: 7583/10000 (76%)
natural_err_total:  tensor(2417., device='cuda:0')
robust_err_total:  tensor(5212., device='cuda:0')
================================================================
Train Epoch: 69 [0/50000 (0%)]	Loss: 1.226026
Train Epoch: 69 [12800/50000 (26%)]	Loss: 1.297620
Train Epoch: 69 [25600/50000 (51%)]	Loss: 1.192744
Train Epoch: 69 [38400/50000 (77%)]	Loss: 1.164126
================================================================
Training: Average loss: 0.8240, Accuracy: 41636/50000 (83%)
Test: Average loss: 0.8965, Accuracy: 7735/10000 (77%)
natural_err_total:  tensor(2265., device='cuda:0')
robust_err_total:  tensor(5161., device='cuda:0')
================================================================
Train Epoch: 70 [0/50000 (0%)]	Loss: 1.157933
Train Epoch: 70 [12800/50000 (26%)]	Loss: 1.182477
Train Epoch: 70 [25600/50000 (51%)]	Loss: 1.244811
Train Epoch: 70 [38400/50000 (77%)]	Loss: 1.141664
================================================================
Training: Average loss: 0.8044, Accuracy: 41656/50000 (83%)
Test: Average loss: 0.8853, Accuracy: 7718/10000 (77%)
natural_err_total:  tensor(2282., device='cuda:0')
robust_err_total:  tensor(5210., device='cuda:0')
================================================================
Train Epoch: 71 [0/50000 (0%)]	Loss: 1.152207
Train Epoch: 71 [12800/50000 (26%)]	Loss: 1.181764
Train Epoch: 71 [25600/50000 (51%)]	Loss: 1.203603
Train Epoch: 71 [38400/50000 (77%)]	Loss: 1.143099
================================================================
Training: Average loss: 0.8307, Accuracy: 41388/50000 (83%)
Test: Average loss: 0.9048, Accuracy: 7706/10000 (77%)
natural_err_total:  tensor(2294., device='cuda:0')
robust_err_total:  tensor(5200., device='cuda:0')
================================================================
Train Epoch: 72 [0/50000 (0%)]	Loss: 1.054137
Train Epoch: 72 [12800/50000 (26%)]	Loss: 1.185718
Train Epoch: 72 [25600/50000 (51%)]	Loss: 1.101494
Train Epoch: 72 [38400/50000 (77%)]	Loss: 1.293612
================================================================
Training: Average loss: 0.8412, Accuracy: 41198/50000 (82%)
Test: Average loss: 0.9219, Accuracy: 7656/10000 (77%)
natural_err_total:  tensor(2344., device='cuda:0')
robust_err_total:  tensor(5191., device='cuda:0')
================================================================
Train Epoch: 73 [0/50000 (0%)]	Loss: 1.172971
Train Epoch: 73 [12800/50000 (26%)]	Loss: 1.188880
Train Epoch: 73 [25600/50000 (51%)]	Loss: 1.160738
Train Epoch: 73 [38400/50000 (77%)]	Loss: 1.148988
================================================================
Training: Average loss: 0.8478, Accuracy: 41318/50000 (83%)
Test: Average loss: 0.9216, Accuracy: 7689/10000 (77%)
natural_err_total:  tensor(2311., device='cuda:0')
robust_err_total:  tensor(5096., device='cuda:0')
================================================================
Train Epoch: 74 [0/50000 (0%)]	Loss: 1.165325
Train Epoch: 74 [12800/50000 (26%)]	Loss: 1.189229
Train Epoch: 74 [25600/50000 (51%)]	Loss: 1.069013
Train Epoch: 74 [38400/50000 (77%)]	Loss: 1.247487
================================================================
Training: Average loss: 0.8467, Accuracy: 40579/50000 (81%)
Test: Average loss: 0.9217, Accuracy: 7523/10000 (75%)
natural_err_total:  tensor(2477., device='cuda:0')
robust_err_total:  tensor(5321., device='cuda:0')
================================================================
Train Epoch: 75 [0/50000 (0%)]	Loss: 1.211550
Train Epoch: 75 [12800/50000 (26%)]	Loss: 1.041979
Train Epoch: 75 [25600/50000 (51%)]	Loss: 1.028928
Train Epoch: 75 [38400/50000 (77%)]	Loss: 0.952108
================================================================
Training: Average loss: 0.6987, Accuracy: 43831/50000 (88%)
Test: Average loss: 0.8013, Accuracy: 8065/10000 (81%)
natural_err_total:  tensor(1935., device='cuda:0')
robust_err_total:  tensor(4726., device='cuda:0')
================================================================
Train Epoch: 76 [0/50000 (0%)]	Loss: 1.037864
Train Epoch: 76 [12800/50000 (26%)]	Loss: 0.948000
Train Epoch: 76 [25600/50000 (51%)]	Loss: 1.135397
Train Epoch: 76 [38400/50000 (77%)]	Loss: 0.912909
================================================================
Training: Average loss: 0.6594, Accuracy: 44638/50000 (89%)
Test: Average loss: 0.7774, Accuracy: 8135/10000 (81%)
natural_err_total:  tensor(1865., device='cuda:0')
robust_err_total:  tensor(4744., device='cuda:0')
================================================================
Train Epoch: 77 [0/50000 (0%)]	Loss: 1.052939
Train Epoch: 77 [12800/50000 (26%)]	Loss: 0.994615
Train Epoch: 77 [25600/50000 (51%)]	Loss: 1.105642
Train Epoch: 77 [38400/50000 (77%)]	Loss: 0.900054
================================================================
Training: Average loss: 0.6578, Accuracy: 44561/50000 (89%)
Test: Average loss: 0.7822, Accuracy: 8104/10000 (81%)
natural_err_total:  tensor(1896., device='cuda:0')
robust_err_total:  tensor(4680., device='cuda:0')
================================================================
Train Epoch: 78 [0/50000 (0%)]	Loss: 1.017923
Train Epoch: 78 [12800/50000 (26%)]	Loss: 0.911335
Train Epoch: 78 [25600/50000 (51%)]	Loss: 1.109883
Train Epoch: 78 [38400/50000 (77%)]	Loss: 1.080931
================================================================
Training: Average loss: 0.6361, Accuracy: 44688/50000 (89%)
Test: Average loss: 0.7717, Accuracy: 8082/10000 (81%)
natural_err_total:  tensor(1918., device='cuda:0')
robust_err_total:  tensor(4722., device='cuda:0')
================================================================
Train Epoch: 79 [0/50000 (0%)]	Loss: 0.941047
Train Epoch: 79 [12800/50000 (26%)]	Loss: 0.954025
Train Epoch: 79 [25600/50000 (51%)]	Loss: 0.879134
Train Epoch: 79 [38400/50000 (77%)]	Loss: 1.006054
================================================================
Training: Average loss: 0.6321, Accuracy: 45023/50000 (90%)
Test: Average loss: 0.7680, Accuracy: 8143/10000 (81%)
natural_err_total:  tensor(1857., device='cuda:0')
robust_err_total:  tensor(4690., device='cuda:0')
================================================================
Train Epoch: 80 [0/50000 (0%)]	Loss: 1.053110
Train Epoch: 80 [12800/50000 (26%)]	Loss: 0.917446
Train Epoch: 80 [25600/50000 (51%)]	Loss: 0.905996
Train Epoch: 80 [38400/50000 (77%)]	Loss: 0.988675
================================================================
Training: Average loss: 0.6138, Accuracy: 45129/50000 (90%)
Test: Average loss: 0.7570, Accuracy: 8127/10000 (81%)
natural_err_total:  tensor(1873., device='cuda:0')
robust_err_total:  tensor(4673., device='cuda:0')
================================================================
Train Epoch: 81 [0/50000 (0%)]	Loss: 1.068628
Train Epoch: 81 [12800/50000 (26%)]	Loss: 0.949077
Train Epoch: 81 [25600/50000 (51%)]	Loss: 1.028734
Train Epoch: 81 [38400/50000 (77%)]	Loss: 1.016144
================================================================
Training: Average loss: 0.6122, Accuracy: 45286/50000 (91%)
Test: Average loss: 0.7597, Accuracy: 8123/10000 (81%)
natural_err_total:  tensor(1877., device='cuda:0')
robust_err_total:  tensor(4710., device='cuda:0')
================================================================
Train Epoch: 82 [0/50000 (0%)]	Loss: 1.021185
Train Epoch: 82 [12800/50000 (26%)]	Loss: 0.955906
Train Epoch: 82 [25600/50000 (51%)]	Loss: 0.930469
Train Epoch: 82 [38400/50000 (77%)]	Loss: 0.881819
================================================================
Training: Average loss: 0.6025, Accuracy: 45234/50000 (90%)
Test: Average loss: 0.7585, Accuracy: 8079/10000 (81%)
natural_err_total:  tensor(1921., device='cuda:0')
robust_err_total:  tensor(4697., device='cuda:0')
================================================================
Train Epoch: 83 [0/50000 (0%)]	Loss: 0.901330
Train Epoch: 83 [12800/50000 (26%)]	Loss: 0.930364
Train Epoch: 83 [25600/50000 (51%)]	Loss: 0.957511
Train Epoch: 83 [38400/50000 (77%)]	Loss: 0.927742
================================================================
Training: Average loss: 0.5845, Accuracy: 45571/50000 (91%)
Test: Average loss: 0.7452, Accuracy: 8131/10000 (81%)
natural_err_total:  tensor(1869., device='cuda:0')
robust_err_total:  tensor(4672., device='cuda:0')
================================================================
Train Epoch: 84 [0/50000 (0%)]	Loss: 0.883661
Train Epoch: 84 [12800/50000 (26%)]	Loss: 0.974942
Train Epoch: 84 [25600/50000 (51%)]	Loss: 1.075271
Train Epoch: 84 [38400/50000 (77%)]	Loss: 0.971522
================================================================
Training: Average loss: 0.5835, Accuracy: 45406/50000 (91%)
Test: Average loss: 0.7450, Accuracy: 8109/10000 (81%)
natural_err_total:  tensor(1891., device='cuda:0')
robust_err_total:  tensor(4756., device='cuda:0')
================================================================
Train Epoch: 85 [0/50000 (0%)]	Loss: 0.870173
Train Epoch: 85 [12800/50000 (26%)]	Loss: 0.798222
Train Epoch: 85 [25600/50000 (51%)]	Loss: 0.955346
Train Epoch: 85 [38400/50000 (77%)]	Loss: 0.941066
================================================================
Training: Average loss: 0.5648, Accuracy: 46033/50000 (92%)
Test: Average loss: 0.7319, Accuracy: 8220/10000 (82%)
natural_err_total:  tensor(1780., device='cuda:0')
robust_err_total:  tensor(4711., device='cuda:0')
================================================================
Train Epoch: 86 [0/50000 (0%)]	Loss: 0.848026
Train Epoch: 86 [12800/50000 (26%)]	Loss: 0.889812
Train Epoch: 86 [25600/50000 (51%)]	Loss: 0.959573
Train Epoch: 86 [38400/50000 (77%)]	Loss: 0.890874
================================================================
Training: Average loss: 0.5807, Accuracy: 45649/50000 (91%)
Test: Average loss: 0.7496, Accuracy: 8105/10000 (81%)
natural_err_total:  tensor(1895., device='cuda:0')
robust_err_total:  tensor(4700., device='cuda:0')
================================================================
Train Epoch: 87 [0/50000 (0%)]	Loss: 0.887334
Train Epoch: 87 [12800/50000 (26%)]	Loss: 0.944533
Train Epoch: 87 [25600/50000 (51%)]	Loss: 0.941915
Train Epoch: 87 [38400/50000 (77%)]	Loss: 0.886428
================================================================
Training: Average loss: 0.5675, Accuracy: 45957/50000 (92%)
Test: Average loss: 0.7374, Accuracy: 8115/10000 (81%)
natural_err_total:  tensor(1885., device='cuda:0')
robust_err_total:  tensor(4694., device='cuda:0')
================================================================
Train Epoch: 88 [0/50000 (0%)]	Loss: 0.966803
Train Epoch: 88 [12800/50000 (26%)]	Loss: 0.970250
Train Epoch: 88 [25600/50000 (51%)]	Loss: 0.956697
Train Epoch: 88 [38400/50000 (77%)]	Loss: 0.866454
================================================================
Training: Average loss: 0.5647, Accuracy: 45857/50000 (92%)
Test: Average loss: 0.7428, Accuracy: 8117/10000 (81%)
natural_err_total:  tensor(1883., device='cuda:0')
robust_err_total:  tensor(4717., device='cuda:0')
================================================================
Train Epoch: 89 [0/50000 (0%)]	Loss: 1.012424
Train Epoch: 89 [12800/50000 (26%)]	Loss: 0.884387
Train Epoch: 89 [25600/50000 (51%)]	Loss: 0.806898
Train Epoch: 89 [38400/50000 (77%)]	Loss: 0.873632
================================================================
Training: Average loss: 0.5570, Accuracy: 45916/50000 (92%)
Test: Average loss: 0.7367, Accuracy: 8116/10000 (81%)
natural_err_total:  tensor(1884., device='cuda:0')
robust_err_total:  tensor(4739., device='cuda:0')
================================================================
Train Epoch: 90 [0/50000 (0%)]	Loss: 0.941720
Train Epoch: 90 [12800/50000 (26%)]	Loss: 0.983724
Train Epoch: 90 [25600/50000 (51%)]	Loss: 0.945358
Train Epoch: 90 [38400/50000 (77%)]	Loss: 0.863618
================================================================
Training: Average loss: 0.5374, Accuracy: 46205/50000 (92%)
Test: Average loss: 0.7232, Accuracy: 8160/10000 (82%)
natural_err_total:  tensor(1840., device='cuda:0')
robust_err_total:  tensor(4701., device='cuda:0')
================================================================
Train Epoch: 91 [0/50000 (0%)]	Loss: 0.843938
Train Epoch: 91 [12800/50000 (26%)]	Loss: 0.895092
Train Epoch: 91 [25600/50000 (51%)]	Loss: 0.773050
Train Epoch: 91 [38400/50000 (77%)]	Loss: 0.948186
================================================================
Training: Average loss: 0.5126, Accuracy: 46508/50000 (93%)
Test: Average loss: 0.7021, Accuracy: 8222/10000 (82%)
natural_err_total:  tensor(1778., device='cuda:0')
robust_err_total:  tensor(4682., device='cuda:0')
================================================================
Train Epoch: 92 [0/50000 (0%)]	Loss: 0.870016
Train Epoch: 92 [12800/50000 (26%)]	Loss: 0.813914
Train Epoch: 92 [25600/50000 (51%)]	Loss: 0.915532
Train Epoch: 92 [38400/50000 (77%)]	Loss: 0.929494
================================================================
Training: Average loss: 0.5135, Accuracy: 46498/50000 (93%)
Test: Average loss: 0.7033, Accuracy: 8203/10000 (82%)
natural_err_total:  tensor(1797., device='cuda:0')
robust_err_total:  tensor(4661., device='cuda:0')
================================================================
Train Epoch: 93 [0/50000 (0%)]	Loss: 0.939769
Train Epoch: 93 [12800/50000 (26%)]	Loss: 0.782009
Train Epoch: 93 [25600/50000 (51%)]	Loss: 0.780132
Train Epoch: 93 [38400/50000 (77%)]	Loss: 0.961255
================================================================
Training: Average loss: 0.5053, Accuracy: 46545/50000 (93%)
Test: Average loss: 0.6976, Accuracy: 8216/10000 (82%)
natural_err_total:  tensor(1784., device='cuda:0')
robust_err_total:  tensor(4651., device='cuda:0')
================================================================
Train Epoch: 94 [0/50000 (0%)]	Loss: 0.810730
Train Epoch: 94 [12800/50000 (26%)]	Loss: 0.911130
Train Epoch: 94 [25600/50000 (51%)]	Loss: 0.897332
Train Epoch: 94 [38400/50000 (77%)]	Loss: 0.835674
================================================================
Training: Average loss: 0.5136, Accuracy: 46575/50000 (93%)
Test: Average loss: 0.7044, Accuracy: 8204/10000 (82%)
natural_err_total:  tensor(1796., device='cuda:0')
robust_err_total:  tensor(4653., device='cuda:0')
================================================================
Train Epoch: 95 [0/50000 (0%)]	Loss: 0.882641
Train Epoch: 95 [12800/50000 (26%)]	Loss: 0.821585
Train Epoch: 95 [25600/50000 (51%)]	Loss: 0.865494
Train Epoch: 95 [38400/50000 (77%)]	Loss: 0.808354
================================================================
Training: Average loss: 0.5073, Accuracy: 46695/50000 (93%)
Test: Average loss: 0.6994, Accuracy: 8220/10000 (82%)
natural_err_total:  tensor(1780., device='cuda:0')
robust_err_total:  tensor(4654., device='cuda:0')
================================================================
Train Epoch: 96 [0/50000 (0%)]	Loss: 0.884703
Train Epoch: 96 [12800/50000 (26%)]	Loss: 0.797846
Train Epoch: 96 [25600/50000 (51%)]	Loss: 0.770633
Train Epoch: 96 [38400/50000 (77%)]	Loss: 0.983241
================================================================
Training: Average loss: 0.4987, Accuracy: 46594/50000 (93%)
Test: Average loss: 0.6961, Accuracy: 8194/10000 (82%)
natural_err_total:  tensor(1806., device='cuda:0')
robust_err_total:  tensor(4663., device='cuda:0')
================================================================
Train Epoch: 97 [0/50000 (0%)]	Loss: 0.896651
Train Epoch: 97 [12800/50000 (26%)]	Loss: 0.825700
Train Epoch: 97 [25600/50000 (51%)]	Loss: 0.880754
Train Epoch: 97 [38400/50000 (77%)]	Loss: 0.907114
================================================================
Training: Average loss: 0.5010, Accuracy: 46685/50000 (93%)
Test: Average loss: 0.6965, Accuracy: 8221/10000 (82%)
natural_err_total:  tensor(1779., device='cuda:0')
robust_err_total:  tensor(4664., device='cuda:0')
================================================================
Train Epoch: 98 [0/50000 (0%)]	Loss: 0.882872
Train Epoch: 98 [12800/50000 (26%)]	Loss: 0.761355
Train Epoch: 98 [25600/50000 (51%)]	Loss: 0.940653
Train Epoch: 98 [38400/50000 (77%)]	Loss: 0.834877
================================================================
Training: Average loss: 0.4960, Accuracy: 46818/50000 (94%)
Test: Average loss: 0.6937, Accuracy: 8231/10000 (82%)
natural_err_total:  tensor(1769., device='cuda:0')
robust_err_total:  tensor(4682., device='cuda:0')
================================================================
Train Epoch: 99 [0/50000 (0%)]	Loss: 0.860974
Train Epoch: 99 [12800/50000 (26%)]	Loss: 0.879426
Train Epoch: 99 [25600/50000 (51%)]	Loss: 0.828830
Train Epoch: 99 [38400/50000 (77%)]	Loss: 0.882576
================================================================
Training: Average loss: 0.4994, Accuracy: 46738/50000 (93%)
Test: Average loss: 0.6976, Accuracy: 8211/10000 (82%)
natural_err_total:  tensor(1789., device='cuda:0')
robust_err_total:  tensor(4659., device='cuda:0')
================================================================
Train Epoch: 100 [0/50000 (0%)]	Loss: 0.801792
Train Epoch: 100 [12800/50000 (26%)]	Loss: 0.774647
Train Epoch: 100 [25600/50000 (51%)]	Loss: 0.908670
Train Epoch: 100 [38400/50000 (77%)]	Loss: 0.750496
================================================================
Training: Average loss: 0.4953, Accuracy: 46704/50000 (93%)
Test: Average loss: 0.6945, Accuracy: 8202/10000 (82%)
natural_err_total:  tensor(1798., device='cuda:0')
robust_err_total:  tensor(4668., device='cuda:0')
================================================================
