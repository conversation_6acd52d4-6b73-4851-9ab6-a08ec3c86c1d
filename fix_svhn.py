"""
SVHN数据集标签修复工具

用于修复SVHN数据集中的标签问题：将标签10转换为0
可以单独运行，也可以导入到其他脚本中使用
"""

import torch
import numpy as np

def fix_svhn_labels(dataset):
    """
    修正SVHN标签，确保它们在0-9范围内
    兼容多种标签存储格式，包括PyTorch 0.4+和1.0+版本
    """
    # 处理SVHN的不同标签存储格式
    if hasattr(dataset, 'labels'):
        # 早期PyTorch版本
        if isinstance(dataset.labels, torch.Tensor):
            dataset.labels[dataset.labels == 10] = 0
        else:
            dataset.labels[dataset.labels == 10] = 0
    elif hasattr(dataset, 'targets'):
        # 较新的PyTorch版本
        if isinstance(dataset.targets, torch.Tensor):
            dataset.targets[dataset.targets == 10] = 0
        else:
            dataset.targets[dataset.targets == 10] = 0
    elif hasattr(dataset, 'target'):
        # 某些自定义数据集
        if isinstance(dataset.target, torch.Tensor):
            dataset.target[dataset.target == 10] = 0
        else:
            dataset.target[dataset.target == 10] = 0
    elif hasattr(dataset, '_labels'):
        # SVHN特有格式
        if isinstance(dataset._labels, torch.Tensor):
            dataset._labels[dataset._labels == 10] = 0
        else:
            dataset._labels[dataset._labels == 10] = 0
    # 处理SVHN在内存映射模式下可能的label格式
    elif hasattr(dataset, 'data') and isinstance(dataset.data, dict) and 'labels' in dataset.data:
        dataset.data['labels'][dataset.data['labels'] == 10] = 0
    else:
        print("警告: 无法识别的SVHN标签格式，请手动检查SVHN数据集的标签属性")
    
    return dataset

def fix_svhn_dataset(trainset, testset):
    """
    修复SVHN数据集的标签，将10转换为0
    
    参数:
    trainset: SVHN训练集实例
    testset: SVHN测试集实例
    
    返回:
    修正后的训练集和测试集
    """
    trainset = fix_svhn_labels(trainset)
    testset = fix_svhn_labels(testset)
    
    # 验证修复是否成功
    success = False
    if hasattr(trainset, 'labels'):
        unique_labels = set(trainset.labels.tolist() if isinstance(trainset.labels, torch.Tensor) else trainset.labels.tolist())
        success = 10 not in unique_labels and max(unique_labels) <= 9
    elif hasattr(trainset, 'targets'):
        unique_labels = set(trainset.targets.tolist() if isinstance(trainset.targets, torch.Tensor) else trainset.targets.tolist())
        success = 10 not in unique_labels and max(unique_labels) <= 9
        
    if success:
        print("SVHN数据集标签已成功修复: 所有标签在0-9范围内")
    else:
        print("警告: SVHN数据集标签可能未正确修复，请检查数据集格式")
        
    return trainset, testset

def patch_svhn_dataloader(get_data_loaders_func):
    """
    装饰器，用于修补现有的数据加载函数，使其自动修复SVHN数据集的标签
    
    参数:
    get_data_loaders_func: 原始的数据加载函数
    
    返回:
    修补后的数据加载函数
    """
    def wrapper(*args, **kwargs):
        # 调用原始函数获取数据加载器
        result = get_data_loaders_func(*args, **kwargs)
        
        # 解包结果，兼容Python 3.7
        if isinstance(result, tuple) and len(result) >= 2:
            train_loader = result[0]
            test_loader = result[1]
            rest = result[2:] if len(result) > 2 else []
        else:
            # 如果返回值不是预期的格式，直接返回
            return result
        
        # 检查数据集类型，如果是SVHN则修复
        if 'svhn' in args[0].lower() or kwargs.get('dataset_name', '').lower() == 'svhn':
            print("检测到SVHN数据集，自动修复标签问题...")
            
            # 获取数据集并修复
            trainset = train_loader.dataset
            testset = test_loader.dataset
            trainset, testset = fix_svhn_dataset(trainset, testset)
            
            # 重建数据加载器，保留原来的参数
            batch_size = train_loader.batch_size
            num_workers = train_loader._num_workers
            
            from torch.utils.data import DataLoader
            train_loader = DataLoader(trainset, batch_size=batch_size, shuffle=True, num_workers=num_workers, pin_memory=True)
            test_loader = DataLoader(testset, batch_size=batch_size, shuffle=False, num_workers=num_workers, pin_memory=True)
            
        # 返回结果，保持与原始函数相同的返回格式
        if rest:
            return (train_loader, test_loader) + tuple(rest)
        else:
            return train_loader, test_loader
    
    return wrapper

# 如何使用这个修复工具:
"""
1. 直接导入并使用fix_svhn_dataset函数:

from fix_svhn import fix_svhn_dataset

# 在您的代码中
trainset = torchvision.datasets.SVHN(root=data_path, split='train', download=True, transform=transform_train)
testset = torchvision.datasets.SVHN(root=data_path, split='test', download=True, transform=transform_test)
trainset, testset = fix_svhn_dataset(trainset, testset)
train_loader = DataLoader(trainset, batch_size=batch_size, shuffle=True, num_workers=num_workers)
test_loader = DataLoader(testset, batch_size=batch_size, shuffle=False, num_workers=num_workers)

2. 使用装饰器自动修补现有的数据加载函数:

from fix_svhn import patch_svhn_dataloader

@patch_svhn_dataloader
def get_data_loaders(dataset_name, data_path, batch_size, num_workers):
    # 原始的数据加载代码
    ...
    return train_loader, test_loader, num_classes
"""

if __name__ == "__main__":
    # 简单的测试代码，确认修复函数可以工作
    import torchvision
    from torchvision import transforms
    
    # 创建最基本的数据变换
    transform = transforms.Compose([transforms.ToTensor()])
    
    # 下载并加载SVHN数据集
    print("正在下载并加载SVHN数据集...")
    try:
        trainset = torchvision.datasets.SVHN(root='./data', split='train', download=True, transform=transform)
        testset = torchvision.datasets.SVHN(root='./data', split='test', download=True, transform=transform)
        
        # 检查标签情况
        print("\n修复前:")
        if hasattr(trainset, 'labels'):
            unique_labels = sorted(set(trainset.labels.tolist() if isinstance(trainset.labels, torch.Tensor) else trainset.labels.tolist()))
            print(f"训练集标签: {unique_labels}")
        elif hasattr(trainset, 'targets'):
            unique_labels = sorted(set(trainset.targets.tolist() if isinstance(trainset.targets, torch.Tensor) else trainset.targets.tolist()))
            print(f"训练集标签: {unique_labels}")
        
        # 修复标签
        print("\n正在修复标签...")
        trainset, testset = fix_svhn_dataset(trainset, testset)
        
        # 再次检查标签情况
        print("\n修复后:")
        if hasattr(trainset, 'labels'):
            unique_labels = sorted(set(trainset.labels.tolist() if isinstance(trainset.labels, torch.Tensor) else trainset.labels.tolist()))
            print(f"训练集标签: {unique_labels}")
        elif hasattr(trainset, 'targets'):
            unique_labels = sorted(set(trainset.targets.tolist() if isinstance(trainset.targets, torch.Tensor) else trainset.targets.tolist()))
            print(f"训练集标签: {unique_labels}")
            
        print("\nSVHN标签修复工具测试成功!")
    except Exception as e:
        print(f"测试过程中出现错误: {e}") 