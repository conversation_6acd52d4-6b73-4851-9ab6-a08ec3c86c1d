import torch
import torchvision
import numpy as np
import os
from torchvision.datasets import FashionMNIST

print("开始为Fashion-MNIST生成验证集划分...")

# 设置随机种子以确保可重复性
np.random.seed(0)
torch.manual_seed(0)

# 创建数据目录
os.makedirs('./data', exist_ok=True)

# 加载Fashion-MNIST数据集
print("加载Fashion-MNIST数据集...")
train_set = FashionMNIST(root='./data', train=True, download=True)
test_set = FashionMNIST(root='./data', train=False, download=True)

print(f"原始训练集大小: {len(train_set)}个样本")
print(f"原始测试集大小: {len(test_set)}个样本")

# 从训练集中随机选择10%作为验证集
train_size = len(train_set)
val_size = int(train_size * 0.1)  # 10%作为验证集
train_remain_size = train_size - val_size

# 随机排列索引
indices = np.random.permutation(train_size)
train_indices = indices[val_size:]
val_indices = indices[:val_size]

print(f"划分后训练集大小: {len(train_indices)}个样本")
print(f"验证集大小: {len(val_indices)}个样本")

# 保存划分信息
split_data = {
    'train_indices': train_indices.tolist(),
    'val_indices': val_indices.tolist(),
    'val_size': val_size
}

torch.save(split_data, 'fashion_mnist_validation_split.pth')
print("Fashion-MNIST验证集划分已保存为fashion_mnist_validation_split.pth") 