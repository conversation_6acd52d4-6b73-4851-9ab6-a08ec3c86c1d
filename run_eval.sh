#!/bin/bash

# 设置默认值
MODEL=${1:-"ResNet18"}
DATASET=${2:-"cifar10"}
CHECKPOINT=${3:-""}
ATTACKS=${4:-"pgd fgsm cw aa"}
BATCH_SIZE=${5:-128}

# 检查是否提供了checkpoint
if [ -z "$CHECKPOINT" ]; then
    echo "错误: 请提供模型检查点路径"
    echo "用法: ./run_eval.sh [模型] [数据集] [检查点路径] [攻击类型] [批量大小]"
    echo "示例: ./run_eval.sh ResNet18 cifar10 ./cifar10_ResNet18_eps8_alpha2.0_steps20/model_best.pth \"pgd fgsm\""
    exit 1
fi

# 提示SVHN数据集特殊处理
if [ "$DATASET" == "svhn" ]; then
    echo "注意: SVHN数据集会自动修复标签问题（将标签10修正为0）"
fi

# 运行评估
echo "开始评估 $MODEL 在 $DATASET 数据集上的性能，使用攻击: $ATTACKS"
echo "注意: 只会评估指定的攻击类型，如需评估clean数据，请在攻击类型中添加\"clean\""
python evaluate.py --model $MODEL --dataset $DATASET --checkpoint $CHECKPOINT --attack $ATTACKS --batch-size $BATCH_SIZE

# 示例命令
echo ""
echo "其他评估示例:"
echo "1. 仅评估PGD攻击:"
echo "   ./run_eval.sh ResNet50 svhn ./svhn_ResNet50_eps8_alpha2.0_steps20/model_best.pth \"pgd\""
echo ""
echo "2. 评估原始数据和CW攻击:"
echo "   ./run_eval.sh ResNet18 cifar10 ./cifar10_ResNet18_eps8_alpha2.0_steps20/model_best.pth \"clean cw\" 32"
echo ""
echo "3. 评估所有支持的攻击类型:"
echo "   ./run_eval.sh ResNet50 fashion_mnist ./fashion_mnist_ResNet50_eps8_alpha2_steps20_20250722_063842/model_best.pth \"clean pgd fgsm cw aa\"" 