import argparse
import logging
import sys
import time
import math
import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.autograd import Variable
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import torchvision.models as models

# 导入模型
from models import get_model
from wideresnet import WideResNet
from preactresnet import PreActResNet18

# SVHN的准确均值和标准差
svhn_mean = (0.4377, 0.4438, 0.4728)
svhn_std = (0.1980, 0.2010, 0.1970)

# 添加用于normalize的变量
upper_limit, lower_limit = 1, 0

# 不再需要NormalizeModel
# class NormalizeModel(nn.Module):
#     def __init__(self, model, mean, std):
#         super(NormalizeModel, self).__init__()
#         self.model = model
#         self.mean = torch.tensor(mean).view(1, -1, 1, 1).cuda()
#         self.std = torch.tensor(std).view(1, -1, 1, 1).cuda()

#     def forward(self, x):
#         # 归一化输入
#         x_normalized = (x - self.mean) / self.std
#         return self.model(x_normalized)

# 定义mixup数据增强函数
def mixup_data(x, y, alpha=1.0):
'''返回混合输入、成对的标签和lambda值'''
if alpha > 0:
    lam = np.random.beta(alpha, alpha)
else:
    lam = 1

batch_size = x.size()[0]
index = torch.randperm(batch_size).cuda()

mixed_x = lam * x + (1 - lam) * x[index, :]
y_a, y_b = y, y[index]
return mixed_x, y_a, y_b, lam

# 定义mixup损失函数
def mixup_criterion(criterion, pred, y_a, y_b, lam):
return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def get_args():
parser = argparse.ArgumentParser()
parser.add_argument('--batch-size', default=128, type=int)
parser.add_argument('--test-batch-size', default=128, type=int)
parser.add_argument('--data-dir', default='./data', type=str)
parser.add_argument('--epochs', default=200, type=int)
parser.add_argument('--lr-max', default=0.1, type=float)
parser.add_argument('--lr-schedule', default='piecewise', choices=['superconverge', 'piecewise', 'linear', 'onedrop', 'multipledecay', 'cosine'])
parser.add_argument('--lr-one-drop', default=0.01, type=float)
parser.add_argument('--lr-drop-epoch', default=100, type=int)
parser.add_argument('--attack', default='pgd', type=str, choices=['pgd', 'fgsm', 'free', 'none'])
parser.add_argument('--epsilon', default=8, type=int)
parser.add_argument('--pgd-alpha', default=2, type=float)
parser.add_argument('--attack-iters', default=20, type=int)  # 修改默认值为20
parser.add_argument('--restarts', default=1, type=int)
parser.add_argument('--norm', default='l_inf', type=str)
parser.add_argument('--fname', default='svhn_model', type=str)
parser.add_argument('--seed', default=0, type=int)
parser.add_argument('--width-factor', default=10, type=int)
parser.add_argument('--model', default='ResNet18', type=str, choices=['ResNet18', 'PreActResNet18', 'WideResNet', 'ResNet50', 'DenseNet121'])
parser.add_argument('--eval', action='store_true')
parser.add_argument('--val', action='store_true', help='使用验证集')
parser.add_argument('--no-dp', action='store_true', help='禁用DataParallel')
# 添加缺失的参数
parser.add_argument('--l1', default=0, type=float, help='L1正则化强度')
parser.add_argument('--mixup', action='store_true', help='是否使用mixup数据增强')
parser.add_argument('--mixup-alpha', type=float, default=1.0, help='mixup的alpha参数')
parser.add_argument('--chkpt-iters', default=10, type=int, help='多少个epoch保存一次checkpoint')
return parser.parse_args()

# 添加normalize函数
def normalize(X):
mu = torch.tensor(svhn_mean).view(3, 1, 1).cuda()
std = torch.tensor(svhn_std).view(3, 1, 1).cuda()
return (X - mu) / std

# 添加clamp函数
def clamp(X, lower_limit, upper_limit):
# 将浮点数转换为tensor
if isinstance(lower_limit, (int, float)):
    lower_limit = torch.tensor(lower_limit).to(X.device)
if isinstance(upper_limit, (int, float)):
    upper_limit = torch.tensor(upper_limit).to(X.device)
return torch.max(torch.min(X, upper_limit), lower_limit)

def fix_svhn_labels(dataset):
"""修正SVHN标签，确保它们在0-9范围内
兼容多种标签存储格式，包括PyTorch 0.4+和1.0+版本
"""
# 处理SVHN的不同标签存储格式
if hasattr(dataset, 'labels'):
    # 早期PyTorch版本
    if isinstance(dataset.labels, torch.Tensor):
        dataset.labels[dataset.labels == 10] = 0
    else:
        dataset.labels[dataset.labels == 10] = 0
elif hasattr(dataset, 'targets'):
    # 较新的PyTorch版本
    if isinstance(dataset.targets, torch.Tensor):
        dataset.targets[dataset.targets == 10] = 0
    else:
        dataset.targets[dataset.targets == 10] = 0
elif hasattr(dataset, 'target'):
    # 某些自定义数据集
    if isinstance(dataset.target, torch.Tensor):
        dataset.target[dataset.target == 10] = 0
    else:
        dataset.target[dataset.target == 10] = 0
elif hasattr(dataset, '_labels'):
    # SVHN特有格式
    if isinstance(dataset._labels, torch.Tensor):
        dataset._labels[dataset._labels == 10] = 0
    else:
        dataset._labels[dataset._labels == 10] = 0
# 处理SVHN在内存映射模式下可能的label格式
elif hasattr(dataset, 'data') and isinstance(dataset.data, dict) and 'labels' in dataset.data:
    dataset.data['labels'][dataset.data['labels'] == 10] = 0

return dataset

def attack_pgd(model, X, y, epsilon, alpha, attack_iters, restarts, norm, early_stop=False,
            mixup=False, y_a=None, y_b=None, lam=None):
# 在原始像素空间进行PGD攻击，然后再归一化输入模型
# 创建归一化/逆归一化函数
def normalize(X):
    return (X - torch.tensor(svhn_mean).view(1, 3, 1, 1).cuda()) / torch.tensor(svhn_std).view(1, 3, 1, 1).cuda()

max_loss = torch.zeros(y.shape[0]).cuda()
max_delta = torch.zeros_like(X).cuda()
for _ in range(restarts):
    delta = torch.zeros_like(X).cuda()
    if norm == "l_inf":
        delta.uniform_(-epsilon, epsilon)
    elif norm == "l_2":
        delta.normal_()
        delta = delta.renorm(p=2, dim=1, maxnorm=epsilon)
    else:
        raise ValueError
    delta.requires_grad = True
    for _ in range(attack_iters):
        # 归一化后输入模型
        output = model(normalize(X + delta))
        if early_stop:
            index = torch.where(output.max(1)[1] == y)[0]
        else:
            index = slice(None,None,None)
        if not isinstance(index, slice) and len(index) == 0:
            break
        if mixup:
            criterion = nn.CrossEntropyLoss()
            loss = mixup_criterion(criterion, model(normalize(X+delta)), y_a, y_b, lam)
        else:
            loss = F.cross_entropy(output, y)
        loss.backward()
        grad = delta.grad.detach()
        d = delta[index, :, :, :]
        g = grad[index, :, :, :]
        x = X[index, :, :, :]
        if norm == "l_inf":
            d = torch.clamp(d + alpha * torch.sign(g), -epsilon, epsilon)
        elif norm == "l_2":
            g_norm = torch.norm(g.view(g.shape[0],-1),dim=1).view(-1,1,1,1)
            scaled_g = g/(g_norm + 1e-10)
            d = (d + scaled_g*alpha).view(d.size(0),-1).renorm(p=2,dim=0,maxnorm=epsilon).view_as(d)
        # 确保扰动后的图像在[0,1]范围内
        d = torch.clamp(d, 0 - x, 1 - x)
        delta.data[index, :, :, :] = d
        delta.grad.zero_()
    if mixup:
        criterion = nn.CrossEntropyLoss(reduction='none')
        all_loss = mixup_criterion(criterion, model(normalize(X+delta)), y_a, y_b, lam)
    else:
        all_loss = F.cross_entropy(model(normalize(X+delta)), y, reduction='none')
    max_delta[all_loss >= max_loss] = delta.detach()[all_loss >= max_loss]
    max_loss = torch.max(max_loss, all_loss)
return max_delta

def get_data_loaders(args):
"""使用与提供的代码相同的方式加载数据，包含归一化"""
transform_train = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize(svhn_mean, svhn_std)  # 添加归一化到transform中
])
transform_test = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize(svhn_mean, svhn_std)  # 添加归一化到transform中
])

train_dataset = datasets.SVHN(
    args.data_dir, split='train', transform=transform_train, download=True)
# 修复SVHN标签
train_dataset = fix_svhn_labels(train_dataset)

test_dataset = datasets.SVHN(
    args.data_dir, split='test', transform=transform_test, download=True)
# 修复SVHN标签
test_dataset = fix_svhn_labels(test_dataset)

# 处理验证集
if args.val:
    logging.info("尝试加载预先生成的SVHN验证集划分...")
    val_split_file = os.path.join(args.data_dir, 'svhn_val_split.pth')
    
    # 创建用于验证集的数据集实例，使用测试集的变换
    val_dataset_base = datasets.SVHN(
        args.data_dir, split='train', transform=transform_test, download=True)
    val_dataset_base = fix_svhn_labels(val_dataset_base)
    
    if os.path.exists(val_split_file):
        # 加载预先生成的划分
        split_data = torch.load(val_split_file)
        train_indices = split_data['train_indices']
        val_indices = split_data['val_indices']
        
        from torch.utils.data import SubsetRandomSampler, Subset
        # 使用原始训练集和验证数据集创建子集
        train_dataset = Subset(train_dataset, train_indices)
        val_dataset = Subset(val_dataset_base, val_indices)
        
        logging.info("成功加载SVHN验证集划分")
    else:
        logging.info("从训练集中分割验证集...")
        train_size = int(0.9 * len(train_dataset))
        val_size = len(train_dataset) - train_size
        
        # 生成并保存划分以便重用
        indices = torch.randperm(len(train_dataset))
        train_indices = indices[:train_size].tolist()
        val_indices = indices[train_size:].tolist()
        
        # 保存划分
        torch.save({
            'train_indices': train_indices,
            'val_indices': val_indices
        }, val_split_file)
        
        from torch.utils.data import Subset
        # 使用原始训练集和验证数据集创建子集
        train_dataset = Subset(train_dataset, train_indices)
        val_dataset = Subset(val_dataset_base, val_indices)
    
    logging.info(f"训练集大小: {len(train_dataset)}个样本")
    logging.info(f"验证集大小: {len(val_dataset)}个样本")
    logging.info(f"测试集大小: {len(test_dataset)}个样本")
    
    train_loader = torch.utils.data.DataLoader(
        dataset=train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        pin_memory=True,
        num_workers=1,
    )
    val_loader = torch.utils.data.DataLoader(
        dataset=val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        pin_memory=True,
        num_workers=1,
    )
    test_loader = torch.utils.data.DataLoader(
        dataset=test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        pin_memory=True,
        num_workers=1,
    )
    return train_loader, val_loader, test_loader
else:
    train_loader = torch.utils.data.DataLoader(
        dataset=train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        pin_memory=True,
        num_workers=1,
    )
    test_loader = torch.utils.data.DataLoader(
        dataset=test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        pin_memory=True,
        num_workers=1,
    )
    return train_loader, None, test_loader

def main():
args = get_args()

if not os.path.exists(args.fname):
    os.makedirs(args.fname)

logger = logging.getLogger(__name__)
logging.basicConfig(
    format='[%(asctime)s] - %(message)s',
    datefmt='%Y/%m/%d %H:%M:%S',
    level=logging.DEBUG,
    handlers=[
        logging.FileHandler(os.path.join(args.fname, 'eval.log' if args.eval else 'output.log')),
        logging.StreamHandler()
    ])

logger.info(args)

np.random.seed(args.seed)
torch.manual_seed(args.seed)
torch.cuda.manual_seed(args.seed)

# 加载数据
if args.val:
    train_loader, val_loader, test_loader = get_data_loaders(args)
else:
    train_loader, _, test_loader = get_data_loaders(args)
    val_loader = None

# 定义模型
if args.model == 'ResNet18':
    model = get_model(args.model, num_classes=10, in_channels=3)
elif args.model == 'ResNet50':
    model = get_model(args.model, num_classes=10, in_channels=3)
elif args.model == 'DenseNet121':
    model = get_model(args.model, num_classes=10, in_channels=3)
elif args.model == 'PreActResNet18':
    model = PreActResNet18()
elif args.model == 'WideResNet':
    model = WideResNet(34, 10, widen_factor=args.width_factor, dropRate=0.0)
else:
    raise ValueError("未知模型类型")

# 将模型移至GPU
if args.no_dp or torch.cuda.device_count() == 1:
    model = model.cuda()
else:
    model = nn.DataParallel(model).cuda()

# 不再需要NormalizeModel包装
# model = NormalizeModel(model, svhn_mean, svhn_std)

# 设置优化器
optimizer = optim.SGD(model.parameters(), lr=args.lr_max, momentum=0.9, weight_decay=5e-4)

# 将epsilon和alpha转换为[0,1]范围
epsilon = args.epsilon / 255.
pgd_alpha = args.pgd_alpha / 255.

# 设置起始epoch和总epochs
start_epoch = 0

# 初始化最佳准确率
best_test_robust_acc = 0
best_val_robust_acc = 0

# 添加缺失的criterion变量
criterion = nn.CrossEntropyLoss()

# 打印表头
logger.info('Epoch \t Train Time \t Test Time \t LR \t \t Train Loss \t Train Acc \t Train Robust Loss \t Train Robust Acc \t Test Loss \t Test Acc \t Test Robust Loss \t Test Robust Acc')

# 定义调度器
if args.lr_schedule == 'piecewise':
    def lr_schedule(t):
        if t < 100:
            return args.lr_max
        elif t < 150:
            return args.lr_max * 0.1
        else:
            return args.lr_max * 0.01
elif args.lr_schedule == 'linear':
    def lr_schedule(t):
        return args.lr_max * (1 - t / args.epochs)
elif args.lr_schedule == 'cosine':
    def lr_schedule(t):
        return args.lr_max * 0.5 * (1 + np.cos(t / args.epochs * np.pi))

# 训练/评估循环与原始代码保持一致

for epoch in range(start_epoch, args.epochs):
    model.train()
    start_time = time.time()
    train_loss = 0
    train_acc = 0
    train_robust_loss = 0
    train_robust_acc = 0
    train_n = 0
    for i, (X, y) in enumerate(train_loader):
        if args.eval:
            break
        X, y = X.cuda(), y.cuda()
        if args.mixup:
            X, y_a, y_b, lam = mixup_data(X, y, args.mixup_alpha)
            X, y_a, y_b = map(Variable, (X, y_a, y_b))
        lr = lr_schedule(epoch + (i + 1) / len(train_loader))
        optimizer.param_groups[0].update(lr=lr)

        if args.attack == 'pgd':
            # Random initialization
            if args.mixup:
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm, mixup=True, y_a=y_a, y_b=y_b, lam=lam)
            else:
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
            delta = delta.detach()

        # Standard training
        elif args.attack == 'none':
            delta = torch.zeros_like(X)

        robust_output = model(clamp(X + delta[:X.size(0)], lower_limit, upper_limit))
        if args.mixup:
            robust_loss = mixup_criterion(criterion, robust_output, y_a, y_b, lam)
        else:
            robust_loss = criterion(robust_output, y)

        if args.l1:
            for name,param in model.named_parameters():
                if 'bn' not in name and 'bias' not in name:
                    robust_loss += args.l1*param.abs().sum()

        optimizer.zero_grad()
        robust_loss.backward()
        optimizer.step()

        output = model(X)
        if args.mixup:
            loss = mixup_criterion(criterion, output, y_a, y_b, lam)
        else:
            loss = criterion(output, y)

        train_robust_loss += robust_loss.item() * y.size(0)
        train_robust_acc += (robust_output.max(1)[1] == y).sum().item()
        train_loss += loss.item() * y.size(0)
        train_acc += (output.max(1)[1] == y).sum().item()
        train_n += y.size(0)

    train_time = time.time()

    # 评估阶段
    model.eval()
    
    # 不评估测试集，只评估验证集
    if val_loader is not None:
        val_loss = 0
        val_acc = 0
        val_robust_loss = 0
        val_robust_acc = 0
        val_n = 0
        
        for i, (X, y) in enumerate(val_loader):
            X, y = X.cuda(), y.cuda()
            
            # 原始准确率
            with torch.no_grad():
                output = model(X)
                loss = F.cross_entropy(output, y)
                val_loss += loss.item() * y.size(0)
                val_acc += (output.max(1)[1] == y).sum().item()
            
            # 鲁棒性评估
            if args.attack != 'none':
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                with torch.no_grad():
                    output = model(X + delta)
                    robust_loss = F.cross_entropy(output, y)
                    val_robust_loss += robust_loss.item() * y.size(0)
                    val_robust_acc += (output.max(1)[1] == y).sum().item()
            
            val_n += y.size(0)
        
        # 计算验证集指标
        val_loss /= val_n
        val_acc = 100.0 * val_acc / val_n
        if args.attack != 'none':
            val_robust_loss /= val_n
            val_robust_acc = 100.0 * val_robust_acc / val_n
    
    # 计算训练指标
    train_loss /= train_n
    train_acc = 100.0 * train_acc / train_n
    
    # 不计算测试集指标
    test_loss = 0
    test_acc = 0
    test_robust_loss = 0
    test_robust_acc = 0
    
    if args.attack != 'none':
        train_robust_loss /= train_n
        train_robust_acc = 100.0 * train_robust_acc / train_n
    
    # 打印结果
    if val_loader is not None:
        logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
        logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"【验证指标】 Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
        if args.attack != 'none':
            logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
            logger.info(f"【验证鲁棒】 Loss: {val_robust_loss:.4f}, Acc: {val_robust_acc:.2f}%")
    else:
        logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
        logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"【测试指标】 Loss: {test_loss:.4f}, Acc: {test_acc:.2f}%")
        if args.attack != 'none':
            logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
            logger.info(f"【测试鲁棒】 Loss: {test_robust_loss:.4f}, Acc: {test_robust_acc:.2f}%")
    
    # 保存模型检查点
    torch.save(model.state_dict(), os.path.join(args.fname, f'model_epoch{epoch}.pth'))
    
    # 保存最佳模型
    if args.attack != 'none':
        # 根据验证集鲁棒准确率选择最佳模型
        if val_loader is not None and val_robust_acc > best_val_robust_acc:
            best_val_robust_acc = val_robust_acc
            torch.save({
                'state_dict': model.state_dict(),
                'val_robust_acc': best_val_robust_acc,
                'epoch': epoch,
            }, os.path.join(args.fname, 'model_best.pth'))
        # 不再根据测试集选择最佳模型
        
        if (epoch+1) % args.chkpt_iters == 0 or epoch+1 == args.epochs:
            torch.save(model.state_dict(), os.path.join(args.fname, f'model_{epoch}.pth'))
            torch.save(optimizer.state_dict(), os.path.join(args.fname, f'opt_{epoch}.pth'))
    else:
        logger.info('%d \t %.1f \t \t %.1f \t \t %.4f \t %.4f \t %.4f \t %.4f \t \t %.4f \t \t %.4f \t %.4f \t %.4f \t \t %.4f',
            epoch, train_time - start_time, -1, lr,
            train_loss/train_n, train_acc/train_n, train_robust_loss/train_n, train_robust_acc/train_n,
            test_loss/test_n, test_acc/test_n, test_robust_loss/test_n, test_robust_acc/test_n)
        return


if __name__ == "__main__":
    main()