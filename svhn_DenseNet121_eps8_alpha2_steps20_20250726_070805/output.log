[2025/07/26 07:08:06] - Namespace(attack='pgd', attack_iters=20, batch_size=128, chkpt_iters=10, data_dir='./data', epochs=200, epsilon=8, eval=False, fname='svhn_DenseNet121_eps8_alpha2_steps20_20250726_070805', l1=0, lr_drop_epoch=100, lr_max=0.005, lr_one_drop=0.01, lr_schedule='piecewise', mixup=False, mixup_alpha=1.0, model='DenseNet121', no_dp=True, norm='l_inf', pgd_alpha=2.0, restarts=1, seed=0, test_batch_size=128, val=True, width_factor=10)
[2025/07/26 07:08:09] - 尝试加载预先生成的SVHN验证集划分...
[2025/07/26 07:08:11] - 成功加载SVHN验证集划分
[2025/07/26 07:08:11] - 训练集大小: 65931个样本
[2025/07/26 07:08:11] - 验证集大小: 7326个样本
[2025/07/26 07:08:11] - 测试集大小: 26032个样本
[2025/07/26 07:08:16] - Epoch 	 Train Time 	 Test Time 	 LR 	 	 Train Loss 	 Train Acc 	 Train Robust Loss 	 Train Robust Acc 	 Test Loss 	 Test Acc 	 Test Robust Loss 	 Test Robust Acc
