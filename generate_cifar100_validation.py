import torch
import torchvision
import numpy as np
import os

print("开始为CIFAR-100生成验证集划分...")

# 设置随机种子以确保可重复性
np.random.seed(0)
torch.manual_seed(0)

# 创建数据目录
os.makedirs('./data', exist_ok=True)

# 下载CIFAR-100数据集
print("加载CIFAR-100数据集...")
cifar100_train = torchvision.datasets.CIFAR100(root='./data', train=True, download=True)
cifar100_test = torchvision.datasets.CIFAR100(root='./data', train=False, download=True)

# 将CIFAR100的测试集的前5000个样本作为验证集
test_data = cifar100_test.data
test_targets = cifar100_test.targets

print(f"原始训练集大小: {len(cifar100_train.data)}个样本")
print(f"原始测试集大小: {len(cifar100_test.data)}个样本")

# 随机打乱测试集
n_test = len(test_targets)
P_test = np.random.permutation(n_test)
test_data = test_data[P_test]
test_targets = [test_targets[p] for p in P_test]

# 从测试集中分出5000个样本作为验证集
val_size = 5000
val_data = test_data[:val_size]
val_targets = test_targets[:val_size]
new_test_data = test_data[val_size:]
new_test_targets = test_targets[val_size:]

# 创建保存的数据结构
dataset = {
    'train': {
        'data': cifar100_train.data,
        'labels': cifar100_train.targets
    },
    'test': {
        'data': new_test_data,
        'labels': new_test_targets
    },
    'val': {
        'data': val_data,
        'labels': val_targets
    },
    'split_size': val_size,
    'test_permutation': P_test.tolist()
}

# 保存数据
torch.save(dataset, 'cifar100_validation_split.pth')

print(f"CIFAR-100验证集划分已保存：")
print(f"- 训练集: {len(dataset['train']['labels'])}个样本")
print(f"- 验证集: {len(dataset['val']['labels'])}个样本")
print(f"- 测试集: {len(dataset['test']['labels'])}个样本")
print("文件已保存为cifar100_validation_split.pth") 