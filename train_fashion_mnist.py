import argparse
import logging
import sys
import time
import math
import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.autograd import Variable
from torchvision import datasets, transforms
from torch.utils.data import DataLoader

# 导入模型
from models import get_model
from wideresnet import WideResNet
from preactresnet import PreActResNet18

# Fashion-MNIST的均值和标准差
fashion_mnist_mean = (0.5,)  # 单通道
fashion_mnist_std = (0.5,)   # 单通道

# 添加上下限值
upper_limit = 1.0
lower_limit = 0.0

# 重新实现NormalizeModel类，用于模型的前置归一化
class NormalizeModel(nn.Module):
    def __init__(self, model, mean, std):
        super(NormalizeModel, self).__init__()
        self.model = model
        self.register_buffer('mean', torch.tensor(mean).view(1, -1, 1, 1))
        self.register_buffer('std', torch.tensor(std).view(1, -1, 1, 1))

    def forward(self, x):
        # 确保mean和std在与x相同的设备上
        # 归一化输入
        x_normalized = (x - self.mean.to(x.device)) / self.std.to(x.device)
        return self.model(x_normalized)

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--batch-size', default=128, type=int)
    parser.add_argument('--test-batch-size', default=128, type=int)
    parser.add_argument('--data-dir', default='./data', type=str)
    parser.add_argument('--epochs', default=100, type=int)  # Fashion-MNIST通常收敛更快
    parser.add_argument('--lr-max', default=0.005, type=float)  # 降低学习率以避免过拟合
    parser.add_argument('--lr-schedule', default='piecewise', choices=['superconverge', 'piecewise', 'linear', 'onedrop', 'multipledecay', 'cosine'])
    parser.add_argument('--lr-one-drop', default=0.01, type=float)
    parser.add_argument('--lr-drop-epoch', default=50, type=int)  # Fashion-MNIST调整学习率时间点
    parser.add_argument('--attack', default='pgd', type=str, choices=['pgd', 'fgsm', 'free', 'none'])
    parser.add_argument('--epsilon', default=8, type=int)
    parser.add_argument('--pgd-alpha', default=2, type=float)
    parser.add_argument('--attack-iters', default=20, type=int)
    parser.add_argument('--restarts', default=1, type=int)
    parser.add_argument('--norm', default='l_inf', type=str, choices=['l_inf', 'l_2'])
    parser.add_argument('--fname', default='fashion_mnist_model', type=str)
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--model', default='ResNet18', type=str, choices=['ResNet18', 'PreActResNet18', 'WideResNet', 'ResNet50', 'DenseNet121', 'wide_resnet34_10'])
    parser.add_argument('--eval', action='store_true')
    parser.add_argument('--val', action='store_true', default=True, help='使用验证集（默认启用）')
    parser.add_argument('--no-dp', action='store_true', help='禁用DataParallel')
    return parser.parse_args()

def clamp(X, lower_limit, upper_limit):
    # 将浮点数转换为tensor
    if isinstance(lower_limit, (int, float)):
        lower_limit = torch.tensor(lower_limit).to(X.device)
    if isinstance(upper_limit, (int, float)):
        upper_limit = torch.tensor(upper_limit).to(X.device)
    return torch.max(torch.min(X, upper_limit), lower_limit)

def mixup_data(x, y, alpha=1.0):
    '''Returns mixed inputs, pairs of targets, and lambda'''
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).cuda()

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def attack_pgd(model, X, y, epsilon, alpha, attack_iters, restarts, norm, early_stop=False,
               mixup=False, y_a=None, y_b=None, lam=None):
    # 在原始像素空间[0,1]进行PGD攻击，归一化交给NormalizeModel处理
    max_loss = torch.zeros(y.shape[0]).cuda()
    max_delta = torch.zeros_like(X).cuda()
    for _ in range(restarts):
        delta = torch.zeros_like(X).cuda()
        if norm == "l_inf":
            delta.uniform_(-epsilon, epsilon)
        elif norm == "l_2":
            delta.normal_()
            delta = delta.renorm(p=2, dim=1, maxnorm=epsilon)
        else:
            raise ValueError
        delta.requires_grad = True
        for _ in range(attack_iters):
            # 直接将X+delta输入模型，内部NormalizeModel会处理归一化
            output = model(X + delta)
            if early_stop:
                index = torch.where(output.max(1)[1] == y)[0]
            else:
                index = slice(None,None,None)
            if not isinstance(index, slice) and len(index) == 0:
                break
            if mixup:
                criterion = nn.CrossEntropyLoss()
                loss = mixup_criterion(criterion, output, y_a, y_b, lam)
            else:
                loss = F.cross_entropy(output, y)
            loss.backward()
            grad = delta.grad.detach()
            d = delta[index, :, :, :]
            g = grad[index, :, :, :]
            x = X[index, :, :, :]
            if norm == "l_inf":
                d = torch.clamp(d + alpha * torch.sign(g), -epsilon, epsilon)
            elif norm == "l_2":
                g_norm = torch.norm(g.view(g.shape[0],-1),dim=1).view(-1,1,1,1)
                scaled_g = g/(g_norm + 1e-10)
                d = (d + scaled_g*alpha).view(d.size(0),-1).renorm(p=2,dim=0,maxnorm=epsilon).view_as(d)
            # 确保扰动后的图像在[0,1]范围内
            d = torch.clamp(d, -x, 1 - x)
            delta.data[index, :, :, :] = d
            delta.grad.zero_()
        if mixup:
            criterion = nn.CrossEntropyLoss(reduction='none')
            all_loss = mixup_criterion(criterion, model(X+delta), y_a, y_b, lam)
        else:
            all_loss = F.cross_entropy(model(X+delta), y, reduction='none')
        max_delta[all_loss >= max_loss] = delta.detach()[all_loss >= max_loss]
        max_loss = torch.max(max_loss, all_loss)
    return max_delta

def get_data_loaders(data_path, batch_size, num_workers=2, val=False):
    """加载Fashion-MNIST数据，但现在transform中只保留ToTensor()"""
    # 添加RandomErasing数据增强以增强鲁棒性
    transform_train = transforms.Compose([
        transforms.Resize(32),
        # 增加数据增强
        transforms.RandomCrop(32, padding=4),  # 随机裁剪
        transforms.ToTensor(),  # 先转换为Tensor
        transforms.RandomErasing(p=0.5, scale=(0.02, 0.33)),  # 随机擦除（必须在ToTensor后应用）
    ])
    transform_test = transforms.Compose([
        transforms.Resize(32),
        transforms.ToTensor(),  # 只保留ToTensor，去除Normalize
    ])
    
    trainset = datasets.FashionMNIST(root=data_path, train=True, download=True, transform=transform_train)
    testset = datasets.FashionMNIST(root=data_path, train=False, download=True, transform=transform_test)
    
    # 处理验证集
    if val:
        # 使用预设的验证集划分
        logging.info("尝试加载预先生成的Fashion-MNIST验证集划分...")
        val_split_file = 'fashion_mnist_validation_split.pth'
        
        # 从训练集中分割验证集，但使用测试集的变换
        valset = datasets.FashionMNIST(root=data_path, train=True, download=True, transform=transform_test)
        
        if os.path.exists(val_split_file):
            # 加载预先生成的划分
            split_data = torch.load(val_split_file)
            train_indices = split_data['train_indices']
            val_indices = split_data['val_indices']
            logging.info(f"成功加载Fashion-MNIST验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        else:
            logging.info("预设验证集划分文件不存在，创建新的划分...")
            train_size = int(0.9 * len(trainset))
            val_size = len(trainset) - train_size
            indices = list(range(len(trainset)))
            np.random.shuffle(indices)
            train_indices = indices[:train_size]
            val_indices = indices[train_size:]
            
            # 保存划分
            torch.save({
                'train_indices': train_indices,
                'val_indices': val_indices,
                'val_size': val_size
            }, val_split_file)
            logging.info(f"创建并保存新的验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        
        from torch.utils.data import SubsetRandomSampler, Subset
        train_dataset = Subset(trainset, train_indices)
        val_dataset = Subset(valset, val_indices)
        
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        return train_loader, val_loader, test_loader
    else:
        # 不使用验证集
        train_loader = DataLoader(
            trainset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        return train_loader, None, test_loader

def main():
    if not os.path.exists(args.fname):
        os.makedirs(args.fname)

    logger = logging.getLogger(__name__)
    logging.basicConfig(
        format='[%(asctime)s] - %(message)s',
        datefmt='%Y/%m/%d %H:%M:%S',
        level=logging.DEBUG,
        handlers=[
            logging.FileHandler(os.path.join(args.fname, 'eval.log' if args.eval else 'output.log')),
            logging.StreamHandler()
        ])

    logger.info(args)

    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)

    # 加载数据
    train_loader, val_loader, test_loader = get_data_loaders(args.data_dir, args.batch_size, 2, args.val)

    # 定义模型 - 使用单通道输入
    if args.model == 'ResNet18':
        model = get_model(args.model, num_classes=10, in_channels=1)
    elif args.model == 'ResNet50':
        model = get_model(args.model, num_classes=10, in_channels=1)
    elif args.model == 'DenseNet121':
        model = get_model(args.model, num_classes=10, in_channels=1)
    elif args.model == 'PreActResNet18':
        model = PreActResNet18(num_classes=10, in_channels=1)
    elif args.model == 'WideResNet':
        model = WideResNet(34, 10, widen_factor=10, dropRate=0.2, in_channels=1)  # 增加dropout
    elif args.model == 'wide_resnet34_10':
        model = WideResNet(34, 10, widen_factor=10, dropRate=0.2, in_channels=1)  # 增加dropout
    else:
        raise ValueError("未知模型类型")

    # 使用NormalizeModel包装原始模型，恢复归一化逻辑
    model = NormalizeModel(model, fashion_mnist_mean, fashion_mnist_std)
        
    # 将模型移至GPU（包括NormalizeModel及其buffers）
    if args.no_dp or torch.cuda.device_count() == 1:
        model = model.cuda()
    else:
        model = nn.DataParallel(model).cuda()

    # 设置优化器
    # 增加weight_decay值到1e-3以增强正则化
    weight_decay = 1e-3 if args.model == 'DenseNet121' else 5e-4  # 为DenseNet增加更高的weight_decay
    optimizer = optim.SGD(model.parameters(), lr=args.lr_max, momentum=0.9, weight_decay=weight_decay)

    # 将epsilon和alpha直接使用原始值，保持在[0,1]范围
    epsilon = args.epsilon / 255.
    pgd_alpha = args.pgd_alpha / 255.

    # 定义调度器
    if args.lr_schedule == 'piecewise':
        def lr_schedule(t):
            if t < 50:
                return args.lr_max
            elif t < 75:
                return args.lr_max * 0.1
            else:
                return args.lr_max * 0.01
    elif args.lr_schedule == 'linear':
        def lr_schedule(t):
            return args.lr_max * (1 - t / args.epochs)
    elif args.lr_schedule == 'cosine':
        def lr_schedule(t):
            return args.lr_max * 0.5 * (1 + np.cos(t / args.epochs * np.pi))
    elif args.lr_schedule == 'onedrop':
        def lr_schedule(t):
            if t < args.lr_drop_epoch:
                return args.lr_max
            else:
                return args.lr_one_drop
    elif args.lr_schedule == 'multipledecay':
        def lr_schedule(t):
            return args.lr_max * 0.1 ** (t // 25)
    
    # 训练/评估循环

    # 保存初始状态
    if not args.eval:
        torch.save(model.state_dict(), os.path.join(args.fname, 'model_init.pth'))
        torch.save(optimizer.state_dict(), os.path.join(args.fname, 'opt_init.pth'))

    # 记录最佳准确率
    best_test_robust_acc = 0
    best_val_robust_acc = 0

    # 评估模式
    if args.eval:
        # 加载模型
        model.eval()
        # 在测试集上评估
        test_loss = 0
        test_acc = 0
        test_n = 0
        
        for i, (X, y) in enumerate(test_loader):
            X, y = X.cuda(), y.cuda()
            
            # 原始准确率
            with torch.no_grad():
                output = model(X)
                loss = F.cross_entropy(output, y)
                test_loss += loss.item() * y.size(0)
                test_acc += (output.max(1)[1] == y).sum().item()
            
            # 鲁棒性评估
            if args.attack != 'none':
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                with torch.no_grad():
                    output = model(X + delta)
                    robust_loss = F.cross_entropy(output, y)
                    test_robust_loss += robust_loss.item() * y.size(0)
                    test_robust_acc += (output.max(1)[1] == y).sum().item()
            
            test_n += y.size(0)
            
        test_loss /= test_n
        test_acc = 100.0 * test_acc / test_n
        
        print(f"测试准确率: {test_acc:.2f}%")
        return

    # 训练模式
    start_epoch = 0
    for epoch in range(start_epoch, args.epochs):
        # 调整学习率
        lr = lr_schedule(epoch)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        
        # 训练一个epoch
        model.train()
        train_loss = 0
        train_acc = 0
        train_robust_loss = 0
        train_robust_acc = 0
        train_n = 0
        
        for i, (X, y) in enumerate(train_loader):
            X, y = X.cuda(), y.cuda()
            
            # 先计算原始样本的损失和准确率
            with torch.no_grad():
                output_clean = model(X)
                train_loss += F.cross_entropy(output_clean, y).item() * y.size(0)
                train_acc += (output_clean.max(1)[1] == y).sum().item()
            
            # 对抗训练
            if args.attack == 'pgd':
                delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                delta = delta.detach()
            elif args.attack == 'none':
                delta = torch.zeros_like(X)
            
            # 使用对抗样本进行训练
            output = model(X + delta)
            loss = F.cross_entropy(output, y)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 计算对抗样本的损失和准确率
            train_robust_loss += loss.item() * y.size(0)
            train_robust_acc += (output.max(1)[1] == y).sum().item()
            train_n += y.size(0)
            
            # 打印进度
            if (i + 1) % 100 == 0:
                print(f'Epoch: {epoch}, Step: {i+1}/{len(train_loader)}, Loss: {loss.item():.4f}')

        # 测试评估
        model.eval()
        
        # 不评估测试集，只评估验证集
        if val_loader is not None:
            val_loss = 0
            val_acc = 0
            val_robust_loss = 0
            val_robust_acc = 0
            val_n = 0
            
            for i, (X, y) in enumerate(val_loader):
                X, y = X.cuda(), y.cuda()
                
                # 原始准确率
                with torch.no_grad():
                    output = model(X)
                    loss = F.cross_entropy(output, y)
                    val_loss += loss.item() * y.size(0)
                    val_acc += (output.max(1)[1] == y).sum().item()
                
                # 鲁棒性评估
                if args.attack != 'none':
                    delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                    with torch.no_grad():
                        output = model(X + delta)
                        robust_loss = F.cross_entropy(output, y)
                        val_robust_loss += robust_loss.item() * y.size(0)
                        val_robust_acc += (output.max(1)[1] == y).sum().item()
                
                val_n += y.size(0)
            
            # 计算验证集指标
            val_loss /= val_n
            val_acc = 100.0 * val_acc / val_n
            if args.attack != 'none':
                val_robust_loss /= val_n
                val_robust_acc = 100.0 * val_robust_acc / val_n
        
        # 计算指标
        train_loss /= train_n
        train_acc = 100.0 * train_acc / train_n
        
        # 不计算测试集指标
        test_loss = 0
        test_acc = 0
        test_robust_loss = 0
        test_robust_acc = 0
        
        if args.attack != 'none':
            train_robust_loss /= train_n
            train_robust_acc = 100.0 * train_robust_acc / train_n
        
        # 打印结果
        if val_loader is not None:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【验证指标】 Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【验证鲁棒】 Loss: {val_robust_loss:.4f}, Acc: {val_robust_acc:.2f}%")
        else:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【测试指标】 Loss: {test_loss:.4f}, Acc: {test_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【测试鲁棒】 Loss: {test_robust_loss:.4f}, Acc: {test_robust_acc:.2f}%")
        
        # 保存模型检查点
        torch.save(model.state_dict(), os.path.join(args.fname, f'model_epoch{epoch}.pth'))
        
        # 保存最佳模型
        if args.attack != 'none':
            # 根据验证集鲁棒准确率选择最佳模型
            if val_loader is not None and val_robust_acc > best_val_robust_acc:
                best_val_robust_acc = val_robust_acc
                torch.save({
                    'state_dict': model.state_dict(),
                    'val_robust_acc': best_val_robust_acc,
                    'epoch': epoch,
                }, os.path.join(args.fname, 'model_best.pth'))
            # 不再根据测试集选择最佳模型 

if __name__ == "__main__":
    args = get_args()
    main() 