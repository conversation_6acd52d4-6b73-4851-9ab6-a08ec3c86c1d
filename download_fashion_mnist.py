import torch
import torchvision
import os
from torchvision.datasets import FashionMNIST

print("开始下载Fashion-MNIST数据集...")

# 创建数据目录
os.makedirs('./data', exist_ok=True)

# 下载Fashion-MNIST训练集和测试集
train_set = FashionMNIST(root='./data', train=True, download=True)
test_set = FashionMNIST(root='./data', train=False, download=True)

print(f"Fashion-MNIST训练集大小: {len(train_set)}个样本")
print(f"Fashion-MNIST测试集大小: {len(test_set)}个样本")
print(f"图像大小: {train_set[0][0].size()}")
print("Fashion-MNIST数据集已下载完成！") 