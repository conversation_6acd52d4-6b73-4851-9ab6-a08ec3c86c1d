import os
import argparse
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import torchvision.datasets as datasets
import torchvision.transforms as transforms
import torchattacks
import time
import numpy as np
from tqdm import tqdm

# 导入模型定义
from models import get_model
from preactresnet import PreActResNet18
from wideresnet import WideResNet
# 导入SVHN标签修复工具
from fix_svhn import fix_svhn_labels

# 设置日志格式
logging.basicConfig(
    format='[%(asctime)s] - %(message)s',
    datefmt='%Y/%m/%d %H:%M:%S',
    level=logging.INFO)

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model', default='ResNet18')
    parser.add_argument('--batch-size', default=128, type=int)
    parser.add_argument('--data-dir', default='./data', type=str)
    parser.add_argument('--dataset', default='cifar10', type=str, choices=['cifar10', 'cifar100', 'svhn', 'fashion_mnist'])
    parser.add_argument('--epsilon', default=8, type=int)
    parser.add_argument('--alpha', default=2, type=float)
    parser.add_argument('--steps', default=20, type=int)
    parser.add_argument('--norm', default='l_inf', type=str)
    parser.add_argument('--attack', default=['pgd', 'fgsm', 'cw', 'aa'], nargs='+', type=str)
    parser.add_argument('--checkpoint', default=None, type=str, help='Path to model checkpoint')
    parser.add_argument('--no-cuda', action='store_true', default=False)
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--width-factor', default=10, type=int)
    return parser.parse_args()

def get_model_instance(args):
    if args.dataset == 'cifar10':
        num_classes = 10
    elif args.dataset == 'cifar100':
        num_classes = 100
    elif args.dataset == 'svhn':
        num_classes = 10
    elif args.dataset == 'fashion_mnist':
        num_classes = 10
    else:
        raise ValueError(f"Unknown dataset: {args.dataset}")
    
    # 使用models.py中的get_model函数
    in_channels = 1 if args.dataset == 'fashion_mnist' else 3
    return get_model(args.model, num_classes=num_classes, in_channels=in_channels)

def get_loaders(args):
    if args.dataset == 'cifar10':
        mean = (0.4914, 0.4822, 0.4465)
        std = (0.2023, 0.1994, 0.2010)
        
        test_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])
        
        test_dataset = datasets.CIFAR10(args.data_dir, train=False, transform=test_transform, download=True)
        
    elif args.dataset == 'cifar100':
        mean = (0.5071, 0.4867, 0.4408)
        std = (0.2675, 0.2565, 0.2761)
        
        test_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])
        
        test_dataset = datasets.CIFAR100(args.data_dir, train=False, transform=test_transform, download=True)
        
    elif args.dataset == 'svhn':
        mean = (0.4377, 0.4438, 0.4728)
        std = (0.1980, 0.2010, 0.1970)
        
        test_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])
        
        test_dataset = datasets.SVHN(args.data_dir, split='test', transform=test_transform, download=True)
        # 修复SVHN标签问题（将10标签修正为0）
        test_dataset = fix_svhn_labels(test_dataset)
        
    elif args.dataset == 'fashion_mnist':
        # 保持Fashion-MNIST为单通道输入
        mean = (0.5,)  # 单通道均值
        std = (0.5,)   # 单通道标准差
        
        test_transform = transforms.Compose([
            transforms.Resize(32),
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])
        
        test_dataset = datasets.FashionMNIST(args.data_dir, train=False, transform=test_transform, download=True)
    
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4, pin_memory=True)
    
    return test_loader, mean, std

class NormalizeModel(nn.Module):
    """包装模型"""
    def __init__(self, model, mean, std, dataset):
        super(NormalizeModel, self).__init__()
        self.model = model
        self.mean = mean
        self.std = std
        self.dataset = dataset
        
    def forward(self, x):
        return self.model(x)

def evaluate(model, test_loader, attack=None, device=None):
    """评估模型在干净样本或对抗样本上的准确率"""
    model.eval()
    correct = 0
    total = 0
    
    for data, target in tqdm(test_loader, desc="Evaluating"):
        data, target = data.to(device), target.to(device)
        
        if attack is not None:
            data.requires_grad = True
            data = attack(data, target)
            # 不再设置 data.requires_grad = False
        
        with torch.no_grad():
            output = model(data)
        
        _, predicted = output.max(1)
        total += target.size(0)
        correct += predicted.eq(target).sum().item()
    
    accuracy = 100.0 * correct / total
    return accuracy

def main():
    args = get_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 设置设备
    use_cuda = not args.no_cuda and torch.cuda.is_available()
    device = torch.device("cuda" if use_cuda else "cpu")
    
    # 加载数据
    test_loader, mean, std = get_loaders(args)
    
    # 加载模型
    model = get_model_instance(args)
    
    # 加载模型权重
    if args.checkpoint:
        if os.path.isfile(args.checkpoint):
            logging.info(f"Loading checkpoint '{args.checkpoint}'")
            checkpoint = torch.load(args.checkpoint, map_location=device)
            if 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model.load_state_dict(checkpoint)
            logging.info(f"Loaded checkpoint '{args.checkpoint}'")
        else:
            logging.error(f"No checkpoint found at '{args.checkpoint}'")
            return
    else:
        logging.error("No checkpoint provided. Please specify with --checkpoint")
        return
    
    # 将模型移至设备
    model = model.to(device)
    
    # 包装模型以进行标准化
    wrapped_model = NormalizeModel(model, mean, std, args.dataset)
    
    # 初始化攻击
    attacks = {}
    attacks['clean'] = None  # 干净样本评估
    
    eps = args.epsilon / 255.0
    alpha = args.alpha / 255.0
    
    if 'none' not in args.attack:
        for attack_name in args.attack:
            logging.info(f"Initializing attack: {attack_name.upper()}")
            
            if attack_name == 'pgd':
                atk = torchattacks.PGD(wrapped_model, eps=eps, alpha=alpha, steps=args.steps)
            elif attack_name == 'fgsm':
                atk = torchattacks.FGSM(wrapped_model, eps=eps)
            elif attack_name == 'cw':
                if args.batch_size > 64:
                    logging.warning("CW attack is memory-intensive. Consider using a smaller batch size (e.g., --batch-size 32).")
                atk = torchattacks.CW(wrapped_model, c=1, kappa=0, steps=100, lr=0.01)
            elif attack_name == 'aa':
                atk = torchattacks.AutoAttack(wrapped_model, norm='Linf', eps=eps, version='standard', verbose=False)
            else:
                logging.warning(f"Unknown attack '{attack_name}' specified. Skipping.")
                continue
                
            attacks[attack_name] = atk
    
    # 评估模型
    results = {}
    for attack_name, attack in attacks.items():
        start_time = time.time()
        accuracy = evaluate(wrapped_model, test_loader, attack, device)
        elapsed = time.time() - start_time
        
        results[attack_name] = accuracy
        logging.info(f"{attack_name.upper()} - Accuracy: {accuracy:.2f}% (Time: {elapsed:.2f}s)")
    
    # 打印总结
    logging.info("\n" + "="*50)
    logging.info(f"Model: {args.model}, Dataset: {args.dataset}")
    logging.info(f"Checkpoint: {args.checkpoint}")
    logging.info("-"*50)
    for attack_name, accuracy in results.items():
        logging.info(f"{attack_name.upper()} - Accuracy: {accuracy:.2f}%")
    logging.info("="*50)

if __name__ == "__main__":
    main() 