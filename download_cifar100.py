import torchvision
import os

print("开始下载CIFAR-100数据集...")
os.makedirs('./data', exist_ok=True)

# 下载CIFAR-100数据集
cifar100_train = torchvision.datasets.CIFAR100(root='./data', train=True, download=True)
cifar100_test = torchvision.datasets.CIFAR100(root='./data', train=False, download=True)

print(f"CIFAR-100训练集大小: {len(cifar100_train)}个样本")
print(f"CIFAR-100测试集大小: {len(cifar100_test)}个样本")
print("CIFAR-100数据集已成功下载到./data/cifar-100-python/目录") 