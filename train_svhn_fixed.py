"""
SVHN数据集训练脚本（带标签修复）

这个脚本专门用于训练SVHN数据集，并自动修复标签问题。
"""

from __future__ import print_function
import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
import torch.optim as optim
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import time

# 导入修复工具
from fix_svhn import fix_svhn_dataset

# 导入模型
from models.resnet import ResNet18, ResNet50
from models.wideresnet import WideResNet
from models.densenet import DenseNet121

# 参数解析
parser = argparse.ArgumentParser(description='PyTorch SVHN训练（带标签修复）')
parser.add_argument('--batch-size', type=int, default=128, metavar='N',
                    help='训练批次大小 (默认: 128)')
parser.add_argument('--test-batch-size', type=int, default=128, metavar='N',
                    help='测试批次大小 (默认: 128)')
parser.add_argument('--epochs', type=int, default=100, metavar='N',
                    help='训练轮次 (默认: 100)')
parser.add_argument('--lr', type=float, default=0.1, metavar='LR',
                    help='学习率 (默认: 0.1)')
parser.add_argument('--momentum', type=float, default=0.9, metavar='M',
                    help='SGD动量 (默认: 0.9)')
parser.add_argument('--weight-decay', '--wd', default=2e-4,
                    type=float, metavar='W', help='权重衰减')
parser.add_argument('--no-cuda', action='store_true', default=False,
                    help='禁用CUDA训练')
parser.add_argument('--seed', type=int, default=1, metavar='S',
                    help='随机种子 (默认: 1)')
parser.add_argument('--log-interval', type=int, default=100, metavar='N',
                    help='日志记录间隔 (默认: 100批次)')
parser.add_argument('--save-dir', default='./svhn_models',
                    help='模型保存目录')
parser.add_argument('--model', default='resnet50', 
                    choices=['resnet18', 'resnet50', 'wrn', 'densenet121'],
                    help='模型架构')
parser.add_argument('--data-dir', default='./data', 
                    help='数据集目录')
parser.add_argument('--val', action='store_true', default=False,
                    help='在验证集上评估模型')
parser.add_argument('--lr-schedule', default='step', choices=['step', 'cosine', 'multistep'],
                    help='学习率调度类型')
parser.add_argument('--warmup-epochs', type=int, default=0,
                    help='学习率预热的轮次数')
parser.add_argument('--milestones', default='75,90,100', type=str,
                    help='多步调度的里程碑，用逗号分隔')
parser.add_argument('--gamma', type=float, default=0.1,
                    help='学习率衰减因子')
parser.add_argument('--save-freq', type=int, default=10,
                    help='保存检查点的频率（轮次）')
parser.add_argument('--resume', default='', type=str,
                    help='从检查点恢复训练')
parser.add_argument('--evaluate', action='store_true', default=False,
                    help='仅评估模型，不训练')
parser.add_argument('--gpu-id', default='0', type=str,
                    help='使用的GPU ID')

args = parser.parse_args()

# 设置GPU
os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu_id

# 创建保存目录
if not os.path.exists(args.save_dir):
    os.makedirs(args.save_dir)

# 设置设备
use_cuda = not args.no_cuda and torch.cuda.is_available()
torch.manual_seed(args.seed)
device = torch.device("cuda" if use_cuda else "cpu")
kwargs = {'num_workers': 4, 'pin_memory': True} if use_cuda else {}

def load_data():
    """加载SVHN数据集并修复标签"""
    # 数据变换
    transform_train = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4377, 0.4438, 0.4728), (0.1980, 0.2010, 0.1970))
    ])
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4377, 0.4438, 0.4728), (0.1980, 0.2010, 0.1970))
    ])
    
    # 加载数据集
    print("加载SVHN数据集...")
    train_dataset = datasets.SVHN(
        args.data_dir, split='train', transform=transform_train, download=True)
    test_dataset = datasets.SVHN(
        args.data_dir, split='test', transform=transform_test, download=True)
    
    # 修复标签
    print("修复SVHN数据集标签...")
    train_dataset, test_dataset = fix_svhn_dataset(train_dataset, test_dataset)
    
    # 如果使用验证集
    if args.val:
        # 从训练集中分割出验证集
        print("从训练集中分割验证集...")
        train_size = int(0.9 * len(train_dataset))
        val_size = len(train_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            train_dataset, [train_size, val_size])
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                                shuffle=True, **kwargs)
        val_loader = DataLoader(val_dataset, batch_size=args.test_batch_size,
                              shuffle=False, **kwargs)
        test_loader = DataLoader(test_dataset, batch_size=args.test_batch_size,
                               shuffle=False, **kwargs)
        
        return train_loader, val_loader, test_loader
    else:
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size,
                                shuffle=True, **kwargs)
        test_loader = DataLoader(test_dataset, batch_size=args.test_batch_size,
                               shuffle=False, **kwargs)
        
        return train_loader, test_loader, None

def get_model():
    """根据参数选择模型"""
    print(f"使用模型: {args.model}")
    if args.model.lower() == 'resnet18':
        return ResNet18(num_classes=10).to(device)
    elif args.model.lower() == 'resnet50':
        return ResNet50(num_classes=10).to(device)
    elif args.model.lower() == 'densenet121':
        return DenseNet121(num_classes=10).to(device)
    else:  # 默认使用WideResNet
        return WideResNet(depth=34, num_classes=10, widen_factor=10).to(device)

def train(model, train_loader, optimizer, epoch):
    """训练一个轮次"""
    model.train()
    train_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = F.cross_entropy(output, target)
        loss.backward()
        optimizer.step()
        
        train_loss += loss.item()
        pred = output.max(1, keepdim=True)[1]
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)
        
        if batch_idx % args.log_interval == 0:
            print('训练轮次: {} [{}/{} ({:.0f}%)]\t损失: {:.6f}\t准确率: {:.2f}%'.format(
                epoch, batch_idx * len(data), len(train_loader.dataset),
                100. * batch_idx / len(train_loader), loss.item(),
                100. * correct / total))
    
    train_loss /= len(train_loader)
    train_accuracy = correct / len(train_loader.dataset)
    print('训练集: 平均损失: {:.4f}, 准确率: {}/{} ({:.2f}%)'.format(
        train_loss, correct, len(train_loader.dataset),
        100. * train_accuracy))
    
    return train_loss, train_accuracy

def test(model, test_loader):
    """测试模型"""
    model.eval()
    test_loss = 0
    correct = 0
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            test_loss += F.cross_entropy(output, target, reduction='sum').item()
            pred = output.max(1, keepdim=True)[1]
            correct += pred.eq(target.view_as(pred)).sum().item()
    
    test_loss /= len(test_loader.dataset)
    test_accuracy = correct / len(test_loader.dataset)
    
    print('测试集: 平均损失: {:.4f}, 准确率: {}/{} ({:.2f}%)\n'.format(
        test_loss, correct, len(test_loader.dataset),
        100. * test_accuracy))
    
    return test_loss, test_accuracy

def adjust_learning_rate(optimizer, epoch):
    """调整学习率"""
    if args.lr_schedule == 'cosine':
        # 余弦退火学习率
        lr = args.lr * 0.5 * (1 + torch.cos(torch.tensor(epoch) * torch.pi / args.epochs))
    elif args.lr_schedule == 'multistep':
        # 多步学习率
        lr = args.lr
        milestones = [int(x) for x in args.milestones.split(',')]
        for milestone in milestones:
            if epoch >= milestone:
                lr *= args.gamma
    else:  # 默认使用step学习率
        # 步进学习率
        lr = args.lr
        if epoch >= 75:
            lr = args.lr * 0.1
        if epoch >= 90:
            lr = args.lr * 0.01
        if epoch >= 100:
            lr = args.lr * 0.001
    
    # 学习率预热
    if epoch < args.warmup_epochs:
        lr = args.lr * (epoch + 1) / args.warmup_epochs
        
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
    
    return lr

def main():
    # 加载数据
    if args.val:
        train_loader, val_loader, test_loader = load_data()
    else:
        train_loader, test_loader, _ = load_data()
    
    # 创建模型
    model = get_model()
    print(f"模型参数总量: {sum(p.numel() for p in model.parameters())}")
    
    # 优化器
    optimizer = optim.SGD(model.parameters(), lr=args.lr,
                         momentum=args.momentum, weight_decay=args.weight_decay)
    
    # 记录训练过程
    best_acc = 0
    start_epoch = 1
    train_losses = []
    train_accs = []
    test_losses = []
    test_accs = []
    
    # 从检查点恢复
    if args.resume:
        if os.path.isfile(args.resume):
            print(f"=> 从检查点 '{args.resume}' 恢复训练")
            checkpoint = torch.load(args.resume)
            start_epoch = checkpoint['epoch'] + 1
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            best_acc = checkpoint.get('best_acc', 0)
            print(f"=> 已恢复 (epoch {checkpoint['epoch']})")
        else:
            print(f"=> 未找到检查点 '{args.resume}'")
    
    # 仅评估模式
    if args.evaluate:
        print("=> 仅评估模式")
        test_loss, test_acc = test(model, test_loader)
        return
    
    # 开始训练
    start_time = time.time()
    for epoch in range(start_epoch, args.epochs + 1):
        print(f"\n开始第 {epoch} 轮训练")
        
        # 调整学习率
        current_lr = adjust_learning_rate(optimizer, epoch)
        print(f"当前学习率: {current_lr:.6f}")
        
        # 训练和测试
        train_loss, train_acc = train(model, train_loader, optimizer, epoch)
        
        # 如果使用验证集
        if args.val:
            val_loss, val_acc = test(model, val_loader)
            print(f"验证集准确率: {val_acc:.4f}")
            eval_acc = val_acc
        else:
            test_loss, test_acc = test(model, test_loader)
            eval_acc = test_acc
        
        # 记录数据
        train_losses.append(train_loss)
        train_accs.append(train_acc)
        
        if args.val:
            test_losses.append(val_loss)
            test_accs.append(val_acc)
        else:
            test_losses.append(test_loss)
            test_accs.append(test_acc)
        
        # 保存最佳模型
        if eval_acc > best_acc:
            best_acc = eval_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
            }, os.path.join(args.save_dir, f'{args.model}_svhn_best.pth'))
            print(f"保存最佳模型，准确率: {best_acc:.4f}")
        
        # 每N轮保存一次检查点
        if epoch % args.save_freq == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'test_loss': test_losses[-1],
                'train_acc': train_acc,
                'test_acc': test_accs[-1],
                'best_acc': best_acc,
            }, os.path.join(args.save_dir, f'{args.model}_svhn_epoch{epoch}.pth'))
    
    # 保存最终模型
    torch.save({
        'epoch': args.epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'best_acc': best_acc,
    }, os.path.join(args.save_dir, f'{args.model}_svhn_final.pth'))
    
    # 训练完成
    total_time = time.time() - start_time
    print(f"\n训练完成！总用时: {total_time/60:.2f}分钟")
    print(f"最佳{'验证' if args.val else '测试'}准确率: {best_acc:.4f}")
    
    # 尝试绘制训练曲线
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        epochs_range = list(range(start_epoch, args.epochs + 1))
        
        plt.figure(figsize=(12, 5))
        
        # 损失曲线
        plt.subplot(1, 2, 1)
        plt.plot(epochs_range, train_losses, 'b-', label='训练损失')
        plt.plot(epochs_range, test_losses, 'r-', label=f"{'验证' if args.val else '测试'}损失")
        plt.title('损失曲线')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        
        # 准确率曲线
        plt.subplot(1, 2, 2)
        plt.plot(epochs_range, [acc * 100 for acc in train_accs], 'b-', label='训练准确率')
        plt.plot(epochs_range, [acc * 100 for acc in test_accs], 'r-', label=f"{'验证' if args.val else '测试'}准确率")
        plt.title('准确率曲线')
        plt.xlabel('轮次')
        plt.ylabel('准确率 (%)')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(args.save_dir, f'{args.model}_svhn_training.png'))
        print(f"训练曲线已保存至 {args.save_dir}/{args.model}_svhn_training.png")
    except Exception as e:
        print(f"绘图时出错: {e}")

if __name__ == "__main__":
    main() 