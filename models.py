import torch
import torch.nn as nn
import torchvision.models as models

# 已有的模型
from wideresnet import WideResNet
from preactresnet import PreActResNet18

def resnet18(num_classes=10, in_channels=3):
    """ResNet18模型"""
    model = models.resnet18(pretrained=False, num_classes=num_classes)
    # 处理输入通道数不是3的情况
    if in_channels != 3:
        model.conv1 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
    return model

def resnet50(num_classes=10, in_channels=3):
    """ResNet50模型"""
    model = models.resnet50(pretrained=False, num_classes=num_classes)
    # 处理输入通道数不是3的情况
    if in_channels != 3:
        model.conv1 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
    return model

def densenet121(num_classes=10, in_channels=3):
    """DenseNet121模型"""
    model = models.densenet121(pretrained=False, num_classes=num_classes)
    
    # 对所有小图像数据集(32x32)进行特殊处理，不只是Fashion-MNIST
    if num_classes in [10, 100]:  # CIFAR-10, CIFAR-100, SVHN, Fashion-MNIST都是小图像
        # 使用更小的卷积核和步长来避免过度下采样
        if in_channels == 1:
            # 单通道图像(Fashion-MNIST)
            model.features.conv0 = nn.Conv2d(1, 64, kernel_size=3, stride=1, padding=1, bias=False)
        else:
            # RGB图像(CIFAR-10, CIFAR-100, SVHN)
            model.features.conv0 = nn.Conv2d(in_channels, 64, kernel_size=3, stride=1, padding=1, bias=False)
        # 移除最大池化层，以保留更多空间信息
        model.features.pool0 = nn.Identity()
        
    # 处理其他情况的输入通道数
    elif in_channels != 3:
        model.features.conv0 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
    
    # 添加Dropout到DenseBlock的每个层
    # DenseNet中的每个DenseBlock包含多个denselayer
    dropout_rate = 0.2  # 设置dropout比率
    
    # 遍历所有feature模块，为denselayers添加dropout
    for name, module in model.named_modules():
        if 'denselayer' in name:
            # 在DenseLayer的卷积层之间添加Dropout
            if hasattr(module, 'conv1'):
                # 保存原始conv2
                orig_conv2 = module.conv2
                
                # 创建一个Sequential替换原始conv2，包含Dropout和conv2
                module.conv2 = nn.Sequential(
                    nn.Dropout(dropout_rate),
                    orig_conv2
                )
        
    return model

# 函数封装WideResNet34_10
def wideresnet34_10(num_classes=10, in_channels=3):
    """WideResNet34_10模型"""
    return WideResNet(34, num_classes, widen_factor=10, dropRate=0.0, in_channels=in_channels)

# 模型选择函数
def get_model(model_name, num_classes=10, in_channels=3):
    """
    根据模型名称返回对应的模型
    
    Args:
        model_name: 模型名称
        num_classes: 类别数量
        in_channels: 输入通道数
    
    Returns:
        对应的模型实例
    """
    # 标准化模型名称以处理不同的大小写和格式
    model_name_lower = model_name.lower()
    
    if model_name == 'ResNet18' or model_name_lower == 'resnet18':
        model = resnet18(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'ResNet50' or model_name_lower == 'resnet50':
        model = resnet50(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'DenseNet121' or model_name_lower == 'densenet121':
        model = densenet121(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'WideResNet34_10' or model_name_lower == 'wideresnet34_10' or model_name == 'wide_resnet34_10':
        model = wideresnet34_10(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'WideResNet' or model_name_lower == 'wideresnet':
        # 默认当使用WideResNet时创建WideResNet34_10
        model = wideresnet34_10(num_classes=num_classes, in_channels=in_channels)
    elif model_name == 'PreActResNet18' or model_name_lower == 'preactresnet18':
        model = PreActResNet18(num_classes=num_classes, in_channels=in_channels)
    else:
        raise ValueError(f"不支持的模型: {model_name}")
    
    return model 