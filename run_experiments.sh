#!/bin/bash

# 创建数据目录
mkdir -p ./data

# 数据集列表
DATASETS=("cifar10" "cifar100" "svhn" "fashion_mnist" "caltech101")

# 模型列表
MODELS=("ResNet18" "ResNet50" "DenseNet121" "WideResNet34_10")

# 参数设置
EPSILON=8
PGD_ALPHA=2
ATTACK_ITERS=20

# 为每个数据集和模型组合运行实验
for dataset in "${DATASETS[@]}"; do
  for model in "${MODELS[@]}"; do
    # 创建带时间戳的输出目录名
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    OUTPUT_DIR="${dataset}_${model}_eps${EPSILON}_alpha${PGD_ALPHA}_steps${ATTACK_ITERS}_${TIMESTAMP}"
    
    echo "==================================================="
    echo "开始训练 - 数据集: $dataset, 模型: $model"
    echo "输出目录: $OUTPUT_DIR"
    echo "==================================================="
    
    python train_cifar.py \
      --dataset $dataset \
      --model $model \
      --epsilon $EPSILON \
      --pgd-alpha $PGD_ALPHA \
      --attack-iters $ATTACK_ITERS \
      --attack pgd \
      --data-dir ./data \
      --fname $OUTPUT_DIR
    
    echo "训练完成: $dataset - $model"
    echo ""
  done
done

echo "所有实验完成！" 