"""
SVHN数据集标签修复测试脚本

这个脚本用于测试SVHN数据集的标签修复是否有效，
通过对比修复前后的标签分布和训练效果来验证。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import numpy as np
import time

# 导入我们的修复工具
from fix_svhn import fix_svhn_labels, fix_svhn_dataset

# 简单的CNN模型用于测试
class SimpleCNN(nn.Module):
    def __init__(self):
        super(SimpleCNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, 1)
        self.conv2 = nn.Conv2d(32, 64, 3, 1)
        self.dropout1 = nn.Dropout2d(0.25)
        self.dropout2 = nn.Dropout2d(0.5)
        self.fc1 = nn.Linear(64 * 6 * 6, 128)
        self.fc2 = nn.Linear(128, 10)

    def forward(self, x):
        x = self.conv1(x)
        x = F.relu(x)
        x = self.conv2(x)
        x = F.relu(x)
        x = F.max_pool2d(x, 2)
        x = self.dropout1(x)
        x = torch.flatten(x, 1)
        x = self.fc1(x)
        x = F.relu(x)
        x = self.dropout2(x)
        x = self.fc2(x)
        return F.log_softmax(x, dim=1)

def train(model, device, train_loader, optimizer, epoch, log_interval=100):
    model.train()
    train_loss = 0
    correct = 0
    total = 0
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = F.nll_loss(output, target)
        loss.backward()
        optimizer.step()
        
        train_loss += loss.item()
        pred = output.argmax(dim=1, keepdim=True)
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)
        
        if batch_idx % log_interval == 0:
            print('训练轮次: {} [{}/{} ({:.0f}%)]\t损失: {:.6f}\t准确率: {:.2f}%'.format(
                epoch, batch_idx * len(data), len(train_loader.dataset),
                100. * batch_idx / len(train_loader), loss.item(), 
                100. * correct / total))
    
    return train_loss / len(train_loader), correct / total

def test(model, device, test_loader):
    model.eval()
    test_loss = 0
    correct = 0
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            test_loss += F.nll_loss(output, target, reduction='sum').item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()

    test_loss /= len(test_loader.dataset)
    accuracy = correct / len(test_loader.dataset)
    
    print('\n测试集: 平均损失: {:.4f}, 准确率: {}/{} ({:.2f}%)\n'.format(
        test_loss, correct, len(test_loader.dataset),
        100. * accuracy))
    
    return test_loss, accuracy

def main():
    # 参数设置
    batch_size = 128
    test_batch_size = 128
    epochs = 5  # 仅训练几轮作为测试
    lr = 0.01
    use_cuda = torch.cuda.is_available()
    seed = 1
    
    torch.manual_seed(seed)
    device = torch.device("cuda" if use_cuda else "cpu")
    kwargs = {'num_workers': 4, 'pin_memory': True} if use_cuda else {}
    
    # 数据变换
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4377, 0.4438, 0.4728), (0.1980, 0.2010, 0.1970))
    ])
    
    # 加载原始SVHN数据集（未修复）
    print("加载原始SVHN数据集（未修复标签）...")
    original_trainset = datasets.SVHN(root='./data', split='train', download=True, transform=transform)
    original_testset = datasets.SVHN(root='./data', split='test', download=True, transform=transform)
    
    # 检查原始标签分布
    if hasattr(original_trainset, 'labels'):
        unique_labels = sorted(set(original_trainset.labels.tolist() if isinstance(original_trainset.labels, torch.Tensor) else original_trainset.labels))
        print(f"原始训练集标签分布: {unique_labels}")
    elif hasattr(original_trainset, 'targets'):
        unique_labels = sorted(set(original_trainset.targets.tolist() if isinstance(original_trainset.targets, torch.Tensor) else original_trainset.targets))
        print(f"原始训练集标签分布: {unique_labels}")
    
    # 创建原始数据加载器
    original_train_loader = DataLoader(original_trainset, batch_size=batch_size, shuffle=True, **kwargs)
    original_test_loader = DataLoader(original_testset, batch_size=test_batch_size, shuffle=False, **kwargs)
    
    # 加载修复后的SVHN数据集
    print("\n加载修复后的SVHN数据集...")
    fixed_trainset = datasets.SVHN(root='./data', split='train', download=True, transform=transform)
    fixed_testset = datasets.SVHN(root='./data', split='test', download=True, transform=transform)
    fixed_trainset, fixed_testset = fix_svhn_dataset(fixed_trainset, fixed_testset)
    
    # 创建修复后的数据加载器
    fixed_train_loader = DataLoader(fixed_trainset, batch_size=batch_size, shuffle=True, **kwargs)
    fixed_test_loader = DataLoader(fixed_testset, batch_size=test_batch_size, shuffle=False, **kwargs)
    
    # 对比训练效果
    results = []
    
    # 1. 使用原始数据集训练
    print("\n===== 使用原始SVHN数据集（未修复标签）训练 =====")
    original_model = SimpleCNN().to(device)
    original_optimizer = optim.SGD(original_model.parameters(), lr=lr, momentum=0.9)
    
    original_train_losses = []
    original_train_accs = []
    original_test_losses = []
    original_test_accs = []
    
    start_time = time.time()
    for epoch in range(1, epochs + 1):
        train_loss, train_acc = train(original_model, device, original_train_loader, original_optimizer, epoch)
        test_loss, test_acc = test(original_model, device, original_test_loader)
        original_train_losses.append(train_loss)
        original_train_accs.append(train_acc)
        original_test_losses.append(test_loss)
        original_test_accs.append(test_acc)
    
    original_time = time.time() - start_time
    print(f"原始数据集训练时间: {original_time:.2f}秒")
    
    # 2. 使用修复后的数据集训练
    print("\n===== 使用修复后的SVHN数据集训练 =====")
    fixed_model = SimpleCNN().to(device)
    fixed_optimizer = optim.SGD(fixed_model.parameters(), lr=lr, momentum=0.9)
    
    fixed_train_losses = []
    fixed_train_accs = []
    fixed_test_losses = []
    fixed_test_accs = []
    
    start_time = time.time()
    for epoch in range(1, epochs + 1):
        train_loss, train_acc = train(fixed_model, device, fixed_train_loader, fixed_optimizer, epoch)
        test_loss, test_acc = test(fixed_model, device, fixed_test_loader)
        fixed_train_losses.append(train_loss)
        fixed_train_accs.append(train_acc)
        fixed_test_losses.append(test_loss)
        fixed_test_accs.append(test_acc)
    
    fixed_time = time.time() - start_time
    print(f"修复后数据集训练时间: {fixed_time:.2f}秒")
    
    # 对比结果
    print("\n===== 训练效果对比 =====")
    print(f"原始数据集 - 最终训练准确率: {original_train_accs[-1]:.4f}, 测试准确率: {original_test_accs[-1]:.4f}")
    print(f"修复后数据集 - 最终训练准确率: {fixed_train_accs[-1]:.4f}, 测试准确率: {fixed_test_accs[-1]:.4f}")
    print(f"准确率提升: 训练 {(fixed_train_accs[-1] - original_train_accs[-1]) * 100:.2f}%, 测试 {(fixed_test_accs[-1] - original_test_accs[-1]) * 100:.2f}%")
    
    # 绘制训练曲线
    try:
        epochs_range = list(range(1, epochs + 1))
        
        plt.figure(figsize=(12, 10))
        
        # 训练损失
        plt.subplot(2, 2, 1)
        plt.plot(epochs_range, original_train_losses, 'b-', label='原始数据集')
        plt.plot(epochs_range, fixed_train_losses, 'r-', label='修复后数据集')
        plt.title('训练损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        
        # 测试损失
        plt.subplot(2, 2, 2)
        plt.plot(epochs_range, original_test_losses, 'b-', label='原始数据集')
        plt.plot(epochs_range, fixed_test_losses, 'r-', label='修复后数据集')
        plt.title('测试损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        
        # 训练准确率
        plt.subplot(2, 2, 3)
        plt.plot(epochs_range, [acc * 100 for acc in original_train_accs], 'b-', label='原始数据集')
        plt.plot(epochs_range, [acc * 100 for acc in fixed_train_accs], 'r-', label='修复后数据集')
        plt.title('训练准确率')
        plt.xlabel('轮次')
        plt.ylabel('准确率 (%)')
        plt.legend()
        
        # 测试准确率
        plt.subplot(2, 2, 4)
        plt.plot(epochs_range, [acc * 100 for acc in original_test_accs], 'b-', label='原始数据集')
        plt.plot(epochs_range, [acc * 100 for acc in fixed_test_accs], 'r-', label='修复后数据集')
        plt.title('测试准确率')
        plt.xlabel('轮次')
        plt.ylabel('准确率 (%)')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('svhn_fix_comparison.png')
        print("\n训练曲线已保存为 'svhn_fix_comparison.png'")
    except Exception as e:
        print(f"绘图时出错: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main() 