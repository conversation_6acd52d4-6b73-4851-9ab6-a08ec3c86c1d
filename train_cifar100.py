
import argparse
import logging
import sys
import time
import math
import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.autograd import Variable
from torchvision import datasets, transforms
from torch.utils.data import DataLoader

# 导入模型
from models import get_model, wideresnet34_10
from wideresnet import WideResNet
from preactresnet import PreActResNet18

# CIFAR100均值和标准差
cifar100_mean = (0.5071, 0.4867, 0.4408)
cifar100_std = (0.2675, 0.2565, 0.2761)

# 添加全局上下限变量
upper_limit = 1.0
lower_limit = 0.0

# 添加全局归一化函数
def normalize(X):
    """全局normalize函数，用于对输入进行归一化"""
    return (X - torch.tensor(cifar100_mean).view(1, 3, 1, 1).cuda()) / torch.tensor(cifar100_std).view(1, 3, 1, 1).cuda()

# 不再需要NormalizeModel类
# class NormalizeModel(nn.Module):
#     def __init__(self, model, mean, std):
#         super(NormalizeModel, self).__init__()
#         self.model = model
#         self.mean = torch.tensor(mean).view(1, -1, 1, 1).cuda()
#         self.std = torch.tensor(std).view(1, -1, 1, 1).cuda()

#     def forward(self, x):
#         # 归一化输入
#         x_normalized = (x - self.mean) / self.std
#         return self.model(x_normalized)

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--batch-size', default=128, type=int)
    parser.add_argument('--test-batch-size', default=128, type=int)
    parser.add_argument('--data-dir', default='./data', type=str)
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr-max', default=0.05, type=float)  # 降低初始学习率，有助于训练稳定
    parser.add_argument('--lr-schedule', default='piecewise', choices=['superconverge', 'piecewise', 'linear', 'onedrop', 'multipledecay', 'cosine'])
    parser.add_argument('--lr-one-drop', default=0.01, type=float)
    parser.add_argument('--lr-drop-epoch', default=100, type=int)
    parser.add_argument('--attack', default='pgd', type=str, choices=['pgd', 'fgsm', 'free', 'none'])
    parser.add_argument('--epsilon', default=8, type=int)
    parser.add_argument('--pgd-alpha', default=2, type=float)
    parser.add_argument('--attack-iters', default=20, type=int)
    parser.add_argument('--restarts', default=1, type=int)
    parser.add_argument('--norm', default='l_inf', type=str)
    parser.add_argument('--fname', default='cifar100_model', type=str)
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--model', default='ResNet18', type=str, choices=['ResNet18', 'PreActResNet18', 'WideResNet', 'ResNet50', 'DenseNet121', 'wide_resnet34_10'])
    parser.add_argument('--eval', action='store_true')
    parser.add_argument('--val', action='store_true', help='使用验证集')
    parser.add_argument('--no-dp', action='store_true', help='禁用DataParallel')
    parser.add_argument('--resume', default=0, type=int, help='从特定轮次恢复训练')
    parser.add_argument('--weight-decay', default=5e-4, type=float, help='权重衰减，用于防止过拟合')
    parser.add_argument('--mixup', action='store_true', help='使用mixup数据增强')
    parser.add_argument('--mixup-alpha', default=0.2, type=float, help='mixup插值参数')
    parser.add_argument('--l1', default=0.0, type=float, help='L1正则化参数')
    parser.add_argument('--chkpt-iters', default=10, type=int, help='多少个epoch保存一次checkpoint')
    return parser.parse_args()

def clamp(X, lower_limit, upper_limit):
    # 将浮点数转换为tensor
    if isinstance(lower_limit, (int, float)):
        lower_limit = torch.tensor(lower_limit).to(X.device)
    if isinstance(upper_limit, (int, float)):
        upper_limit = torch.tensor(upper_limit).to(X.device)
    return torch.max(torch.min(X, upper_limit), lower_limit)

def mixup_data(x, y, alpha=1.0):
    '''Returns mixed inputs, pairs of targets, and lambda'''
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).cuda()

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def attack_pgd(model, X, y, epsilon, alpha, attack_iters, restarts, norm, early_stop=False,
               mixup=False, y_a=None, y_b=None, lam=None):
    # 在原始像素空间进行PGD攻击，然后再归一化输入模型
    # 使用全局normalize函数
    
    max_loss = torch.zeros(y.shape[0]).cuda()
    max_delta = torch.zeros_like(X).cuda()
    for _ in range(restarts):
        delta = torch.zeros_like(X).cuda()
        if norm == "l_inf":
            delta.uniform_(-epsilon, epsilon)
        elif norm == "l_2":
            delta.normal_()
            delta = delta.renorm(p=2, dim=1, maxnorm=epsilon)
        else:
            raise ValueError
        delta.requires_grad = True
        for _ in range(attack_iters):
            # 归一化后输入模型
            output = model(normalize(X + delta))
            if early_stop:
                index = torch.where(output.max(1)[1] == y)[0]
            else:
                index = slice(None,None,None)
            if not isinstance(index, slice) and len(index) == 0:
                break
            if mixup:
                criterion = nn.CrossEntropyLoss()
                loss = mixup_criterion(criterion, model(normalize(X+delta)), y_a, y_b, lam)
            else:
                loss = F.cross_entropy(output, y)
            loss.backward()
            grad = delta.grad.detach()
            d = delta[index, :, :, :]
            g = grad[index, :, :, :]
            x = X[index, :, :, :]
            if norm == "l_inf":
                d = torch.clamp(d + alpha * torch.sign(g), -epsilon, epsilon)
            elif norm == "l_2":
                g_norm = torch.norm(g.view(g.shape[0],-1),dim=1).view(-1,1,1,1)
                scaled_g = g/(g_norm + 1e-10)
                d = (d + scaled_g*alpha).view(d.size(0),-1).renorm(p=2,dim=0,maxnorm=epsilon).view_as(d)
            # 确保扰动后的图像在[0,1]范围内
            d = torch.clamp(d, 0 - x, 1 - x)
            delta.data[index, :, :, :] = d
            delta.grad.zero_()
        if mixup:
            criterion = nn.CrossEntropyLoss(reduction='none')
            all_loss = mixup_criterion(criterion, model(normalize(X+delta)), y_a, y_b, lam)
        else:
            all_loss = F.cross_entropy(model(normalize(X+delta)), y, reduction='none')
        max_delta[all_loss >= max_loss] = delta.detach()[all_loss >= max_loss]
        max_loss = torch.max(max_loss, all_loss)
    return max_delta

def get_data_loaders(data_dir, batch_size, val=False):
    """使用增强的数据增强方式加载CIFAR100数据，并在transform中进行归一化"""
    transform_train = transforms.Compose([
        transforms.RandomCrop(32, padding=4),
        # 移除RandomHorizontalFlip，保持与验证集一致
        # transforms.RandomHorizontalFlip(),
        # 注释掉额外的数据增强，减少内存占用
        # transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
        # transforms.RandomRotation(degrees=10),
        transforms.ToTensor(),
        transforms.Normalize(cifar100_mean, cifar100_std)  # 添加归一化到transform中
    ])
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(cifar100_mean, cifar100_std)  # 添加归一化到transform中
    ])
    
    trainset = datasets.CIFAR100(root=data_dir, train=True, download=True, transform=transform_train)
    testset = datasets.CIFAR100(root=data_dir, train=False, download=True, transform=transform_test)
    
    # 处理验证集
    if val:
        # 尝试加载预先生成的验证集划分
        val_split_file = os.path.join(data_dir, 'cifar100_validation_split.pth')
        logging.info(f"尝试加载预先生成的CIFAR-100验证集划分...")
        
        # 创建一个使用测试集变换的验证集
        valset = datasets.CIFAR100(root=data_dir, train=True, download=True, transform=transform_test)
        
        if os.path.exists(val_split_file):
            # 加载预先生成的划分
            split_data = torch.load(val_split_file)
            # 检查键名是否存在，处理可能的不一致情况
            if 'train_indices' in split_data:
                train_indices = split_data['train_indices']
                val_indices = split_data['val_indices']
            elif 'train_idx' in split_data:
                train_indices = split_data['train_idx']
                val_indices = split_data['val_idx']
            else:
                # 如果无法找到预期的键名，创建新的划分
                logging.info(f"无法识别验证集划分文件中的键名，创建新的划分...")
                train_size = int(0.9 * len(trainset))
                val_size = len(trainset) - train_size
                indices = list(range(len(trainset)))
                np.random.shuffle(indices)
                train_indices = indices[:train_size]
                val_indices = indices[train_size:]
                
                # 保存新的划分
                torch.save({
                    'train_indices': train_indices,
                    'val_indices': val_indices,
                    'val_size': val_size
                }, val_split_file)
                
            logging.info(f"成功加载CIFAR-100验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        else:
            logging.info(f"预设验证集划分文件{val_split_file}不存在，创建新的划分...")
            train_size = int(0.9 * len(trainset))
            val_size = len(trainset) - train_size
            indices = list(range(len(trainset)))
            np.random.shuffle(indices)
            train_indices = indices[:train_size]
            val_indices = indices[train_size:]
            
            # 保存划分
            torch.save({
                'train_indices': train_indices,
                'val_indices': val_indices,
                'val_size': val_size
            }, val_split_file)
            logging.info(f"创建并保存新的验证集划分，训练集大小: {len(train_indices)}，验证集大小: {len(val_indices)}")
        
        from torch.utils.data import Subset
        train_dataset = Subset(trainset, train_indices)
        val_dataset = Subset(valset, val_indices)
        
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=2, pin_memory=True
        )
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=2, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=2, pin_memory=True
        )
        return train_loader, val_loader, test_loader
    else:
        # 不使用验证集
        train_loader = DataLoader(
            trainset, batch_size=batch_size, shuffle=True,
            num_workers=2, pin_memory=True
        )
        test_loader = DataLoader(
            testset, batch_size=batch_size, shuffle=False,
            num_workers=2, pin_memory=True
        )
        return train_loader, None, test_loader

def init_weights(m):
    """使用更好的初始化方法"""
    if isinstance(m, nn.Conv2d):
        nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
    elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
        nn.init.constant_(m.weight, 1)
        nn.init.constant_(m.bias, 0)
    elif isinstance(m, nn.Linear):
        nn.init.xavier_normal_(m.weight)
        nn.init.constant_(m.bias, 0)

def main():
    args = get_args()

    if not os.path.exists(args.fname):
        os.makedirs(args.fname)

    logger = logging.getLogger(__name__)
    logging.basicConfig(
        format='[%(asctime)s] - %(message)s',
        datefmt='%Y/%m/%d %H:%M:%S',
        level=logging.DEBUG,
        handlers=[
            logging.FileHandler(os.path.join(args.fname,'output.log')),
            logging.StreamHandler()
        ])

    logger.info(args)

    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)

    # 加载数据
    train_loader, val_loader, test_loader = get_data_loaders(args.data_dir, args.batch_size, args.val)

    # 定义模型
    if args.model == 'ResNet18':
        model = get_model(args.model, num_classes=100)
    elif args.model == 'ResNet50':
        model = get_model(args.model, num_classes=100)
    elif args.model == 'DenseNet121':
        model = get_model(args.model, num_classes=100)
        # DenseNet121对小图像使用不同的第一层配置
        logger.info("为CIFAR-100适配DenseNet121: 修改卷积层和池化层")
    elif args.model == 'PreActResNet18':
        model = PreActResNet18(num_classes=100)
    elif args.model == 'WideResNet':
        model = WideResNet(34, 100, widen_factor=10, dropRate=0.2)  # 增加dropout以减轻过拟合
    elif args.model == 'wide_resnet34_10':
        # 直接使用WideResNet构造函数保持参数一致性
        model = WideResNet(34, 100, widen_factor=10, dropRate=0.2)  # 与WideResNet保持一致
    else:
        raise ValueError(f"未知模型类型: {args.model}")
        
    # 应用自定义初始化
    model.apply(init_weights)

    # 将模型移至GPU
    if args.no_dp or torch.cuda.device_count() == 1:
        model = model.cuda()
    else:
        model = nn.DataParallel(model).cuda()
    
    # 不再需要NormalizeModel包装
    # 创建NormalizeModel包装模型
    # model_normalized = NormalizeModel(model, cifar100_mean, cifar100_std)
    model_normalized = model  # 直接使用原始模型，因为归一化已经在transform中完成

    # 设置优化器
    optimizer = optim.SGD(model_normalized.parameters(), lr=args.lr_max, momentum=0.9, weight_decay=args.weight_decay)

    # 将epsilon和alpha转换为[0,1]范围
    epsilon = args.epsilon / 255.
    pgd_alpha = args.pgd_alpha / 255.
    
    # 定义损失函数
    criterion = nn.CrossEntropyLoss()

    # 定义调度器
    if args.lr_schedule == 'piecewise':
        def lr_schedule(t):
            # 增加早期学习率调整，加速CIFAR-100的收敛
            if t < 60:
                return args.lr_max
            elif t < 100:
                return args.lr_max * 0.2
            elif t < 150:
                return args.lr_max * 0.04
            else:
                return args.lr_max * 0.008
    elif args.lr_schedule == 'linear':
        def lr_schedule(t):
            return args.lr_max * (1 - t / args.epochs)
    elif args.lr_schedule == 'cosine':
        def lr_schedule(t):
            return args.lr_max * 0.5 * (1 + np.cos(t / args.epochs * np.pi))


    if args.resume:
        start_epoch = args.resume
        model_normalized.load_state_dict(torch.load(os.path.join(args.fname, f'model_{start_epoch-1}.pth')))
        optimizer.load_state_dict(torch.load(os.path.join(args.fname, f'opt_{start_epoch-1}.pth')))
        logger.info(f'Resuming at epoch {start_epoch}')
    else:
        start_epoch = 0

    if not args.eval:
        logger.info('Epoch \t Train Time \t Test Time \t LR \t \t Train Loss \t Train Acc \t Train Robust Loss \t Train Robust Acc \t Test Loss \t Test Acc \t Test Robust Loss \t Test Robust Acc')
    
    best_test_robust_acc = 0
    best_val_robust_acc = 0
    
    for epoch in range(start_epoch, args.epochs):
        model_normalized.train()
        start_time = time.time()
        train_loss = 0
        train_acc = 0
        train_n = 0
        train_robust_loss = 0
        train_robust_acc = 0
        
        # 添加学习率调度
        lr = lr_schedule(epoch)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        
        for i, (X, y) in enumerate(train_loader):
            X, y = X.cuda(), y.cuda()
            
            # 先计算原始样本的损失和准确率
            with torch.no_grad():
                output_clean = model_normalized(X)
                loss_clean = criterion(output_clean, y)
                train_loss += loss_clean.item() * y.size(0)
                train_acc += (output_clean.max(1)[1] == y).sum().item()
            
            # 对抗训练
            if args.attack == 'pgd':
                delta = attack_pgd(model_normalized, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                delta = delta.detach()
                
                if args.mixup:
                    mixed_X, y_a, y_b, lam = mixup_data(X + delta, y, args.mixup_alpha)
                    output = model_normalized(normalize(mixed_X))
                    loss = mixup_criterion(criterion, output, y_a, y_b, lam)
                else:
                    output = model_normalized(normalize(X + delta))
                    loss = criterion(output, y)
                
                # 计算对抗样本的损失和准确率
                with torch.no_grad():
                    train_robust_loss += loss.item() * y.size(0)
                    train_robust_acc += (output.max(1)[1] == y).sum().item()
            else:
                delta = torch.zeros_like(X)
                output = model_normalized(normalize(X))
                loss = criterion(output, y)

            optimizer.zero_grad()
            loss.backward()
            
            # 添加L1正则化
            if args.l1:
                for name, param in model_normalized.named_parameters():
                    if 'weight' in name and param.requires_grad:
                        loss = loss + args.l1 * torch.sum(torch.abs(param))
            
            optimizer.step()
            
            train_n += y.size(0)

            # 输出训练进度
            if (i + 1) % 100 == 0:
                print(f'Epoch: {epoch}, Step: {i+1}/{len(train_loader)}, Loss: {loss.item():.4f}')
        
        # 评估阶段
        model_normalized.eval()
        
        # 不评估测试集，只评估验证集
        if val_loader is not None:
            val_loss = 0
            val_acc = 0
            val_robust_loss = 0
            val_robust_acc = 0
            val_n = 0
            
            for i, (X, y) in enumerate(val_loader):
                X, y = X.cuda(), y.cuda()
                
                # 原始准确率
                with torch.no_grad():
                    output = model_normalized(X)
                    loss = criterion(output, y)
                    val_loss += loss.item() * y.size(0)
                    val_acc += (output.max(1)[1] == y).sum().item()
                
                # 鲁棒性评估
                if args.attack != 'none':
                    delta = attack_pgd(model_normalized, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                    with torch.no_grad():
                        output = model_normalized(X + delta)
                        robust_loss = criterion(output, y)
                        val_robust_loss += robust_loss.item() * y.size(0)
                        val_robust_acc += (output.max(1)[1] == y).sum().item()
                
                val_n += y.size(0)
            
            # 计算验证集指标
            val_loss /= val_n
            val_acc = 100.0 * val_acc / val_n
            if args.attack != 'none':
                val_robust_loss /= val_n
                val_robust_acc = 100.0 * val_robust_acc / val_n
        
        # 计算指标
        train_loss /= train_n
        train_acc = 100.0 * train_acc / train_n
        
        # 不计算测试集指标
        test_loss = 0
        test_acc = 0
        test_robust_loss = 0
        test_robust_acc = 0
        
        if args.attack != 'none':
            train_robust_loss /= train_n
            train_robust_acc = 100.0 * train_robust_acc / train_n
        
        # 打印结果
        if val_loader is not None:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【验证指标】 Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【验证鲁棒】 Loss: {val_robust_loss:.4f}, Acc: {val_robust_acc:.2f}%")
        else:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【测试指标】 Loss: {test_loss:.4f}, Acc: {test_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【测试鲁棒】 Loss: {test_robust_loss:.4f}, Acc: {test_robust_acc:.2f}%")
        
        # 保存模型检查点
        torch.save(model_normalized.state_dict(), os.path.join(args.fname, f'model_epoch{epoch}.pth'))
        
        # 保存最佳模型
        if args.attack != 'none':
            # 根据验证集鲁棒准确率选择最佳模型
            if val_loader is not None and val_robust_acc > best_val_robust_acc:
                best_val_robust_acc = val_robust_acc
                torch.save({
                    'state_dict': model_normalized.state_dict(),
                    'val_robust_acc': best_val_robust_acc,
                    'epoch': epoch,
                }, os.path.join(args.fname, 'model_best.pth'))
            # 不再根据测试集选择最佳模型
        
        # 保存周期性检查点
        if (epoch+1) % args.chkpt_iters == 0 or epoch+1 == args.epochs:
            torch.save({
                'state_dict': model_normalized.state_dict(),
                'optimizer': optimizer.state_dict(),
                'epoch': epoch,
            }, os.path.join(args.fname, f'checkpoint-epoch{epoch}.pth'))


if __name__ == "__main__":
    main()
