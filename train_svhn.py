import argparse
import logging
import sys
import time
import math
import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.autograd import Variable
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
import torchvision.models as models

# 导入模型
from models import get_model
from wideresnet import WideResNet
from preactresnet import PreActResNet18

# SVHN 的均值和标准差
svhn_mean = (0.4377, 0.4438, 0.4728)
svhn_std = (0.1980, 0.2010, 0.1970)

# 添加用于 clamp 的上下限
upper_limit, lower_limit = 1, 0

# 定义 mixup 数据增强
def mixup_data(x, y, alpha=1.0):
    """返回混合输入、成对的标签和 lambda 值"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).cuda()

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

# 定义 mixup 损失
def mixup_criterion(criterion, pred, y_a, y_b, lam):
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

# 参数解析
def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--batch-size', default=128, type=int)
    parser.add_argument('--test-batch-size', default=128, type=int)
    parser.add_argument('--data-dir', default='./data', type=str)
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr-max', default=0.1, type=float)
    parser.add_argument('--lr-schedule', default='piecewise',
                        choices=['superconverge', 'piecewise', 'linear', 'onedrop', 'multipledecay', 'cosine'])
    parser.add_argument('--lr-one-drop', default=0.01, type=float)
    parser.add_argument('--lr-drop-epoch', default=100, type=int)
    parser.add_argument('--attack', default='pgd', type=str,
                        choices=['pgd', 'fgsm', 'free', 'none'])
    parser.add_argument('--epsilon', default=8, type=int)
    parser.add_argument('--pgd-alpha', default=2, type=float)
    parser.add_argument('--attack-iters', default=20, type=int)
    parser.add_argument('--restarts', default=1, type=int)
    parser.add_argument('--norm', default='l_inf', type=str)
    parser.add_argument('--fname', default='svhn_model', type=str)
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--width-factor', default=10, type=int)
    parser.add_argument('--model', default='ResNet18', type=str,
                        choices=['ResNet18', 'PreActResNet18', 'WideResNet', 'ResNet50', 'DenseNet121'])
    parser.add_argument('--eval', action='store_true')
    parser.add_argument('--val', action='store_true', help='使用验证集')
    parser.add_argument('--no-dp', action='store_true', help='禁用 DataParallel')
    parser.add_argument('--l1', default=0, type=float, help='L1 正则化强度')
    parser.add_argument('--mixup', action='store_true', help='是否使用 mixup 数据增强')
    parser.add_argument('--mixup-alpha', type=float, default=1.0, help='mixup 的 alpha 参数')
    parser.add_argument('--chkpt-iters', default=10, type=int, help='多少个 epoch 保存一次 checkpoint')
    return parser.parse_args()

# 归一化函数
def normalize(X):
    mu = torch.tensor(svhn_mean).view(3, 1, 1).cuda()
    std = torch.tensor(svhn_std).view(3, 1, 1).cuda()
    return (X - mu) / std

# 限幅函数
def clamp(X, lower_limit, upper_limit):
    if isinstance(lower_limit, (int, float)):
        lower_limit = torch.tensor(lower_limit).to(X.device)
    if isinstance(upper_limit, (int, float)):
        upper_limit = torch.tensor(upper_limit).to(X.device)
    return torch.max(torch.min(X, upper_limit), lower_limit)

# SVHN 标签修正
def fix_svhn_labels(dataset):
    """修正 SVHN 标签，确保在 0-9 范围"""
    if hasattr(dataset, 'labels'):
        dataset.labels[dataset.labels == 10] = 0
    elif hasattr(dataset, 'targets'):
        dataset.targets[dataset.targets == 10] = 0
    elif hasattr(dataset, 'target'):
        dataset.target[dataset.target == 10] = 0
    elif hasattr(dataset, '_labels'):
        dataset._labels[dataset._labels == 10] = 0
    elif hasattr(dataset, 'data') and isinstance(dataset.data, dict) and 'labels' in dataset.data:
        dataset.data['labels'][dataset.data['labels'] == 10] = 0
    return dataset

# PGD 攻击函数
def attack_pgd(model, X, y, epsilon, alpha, attack_iters, restarts, norm,
               early_stop=False, mixup=False, y_a=None, y_b=None, lam=None):
    """对输入 X 进行多次 restart 的 PGD 攻击"""
    def _normalize(input):
        return (input - torch.tensor(svhn_mean).view(1, 3, 1, 1).cuda()) / \
               torch.tensor(svhn_std).view(1, 3, 1, 1).cuda()

    max_loss = torch.zeros(y.shape[0]).cuda()
    max_delta = torch.zeros_like(X).cuda()

    for _ in range(restarts):
        delta = torch.zeros_like(X).cuda()
        if norm == "l_inf":
            delta.uniform_(-epsilon, epsilon)
        else:
            delta.normal_()
            delta = delta.renorm(p=2, dim=1, maxnorm=epsilon)
        delta.requires_grad = True

        for _ in range(attack_iters):
            output = model(_normalize(X + delta))
            if early_stop:
                idx = torch.where(output.max(1)[1] == y)[0]
            else:
                idx = slice(None)

            if not isinstance(idx, slice) and len(idx) == 0:
                break

            if mixup:
                criterion = nn.CrossEntropyLoss()
                loss = mixup_criterion(criterion, model(_normalize(X + delta)), y_a, y_b, lam)
            else:
                loss = F.cross_entropy(output, y)

            loss.backward()
            grad = delta.grad.detach()
            d = delta[idx]
            g = grad[idx]
            x = X[idx]

            if norm == "l_inf":
                d = torch.clamp(d + alpha * torch.sign(g), -epsilon, epsilon)
            else:
                g_norm = torch.norm(g.view(g.shape[0], -1), dim=1).view(-1, 1, 1, 1)
                scaled_g = g / (g_norm + 1e-10)
                d = (d + scaled_g * alpha).view(d.size(0), -1) \
                         .renorm(p=2, dim=0, maxnorm=epsilon) \
                         .view_as(d)

            d = torch.clamp(d, -x, 1 - x)
            delta.data[idx] = d
            delta.grad.zero_()

        # 记录最大损失对应的扰动
        if mixup:
            crit = nn.CrossEntropyLoss(reduction='none')
            losses = mixup_criterion(crit, model(_normalize(X + delta)), y_a, y_b, lam)
        else:
            losses = F.cross_entropy(model(_normalize(X + delta)), y, reduction='none')

        mask = losses >= max_loss
        max_delta[mask] = delta.detach()[mask]
        max_loss = torch.max(max_loss, losses)

    return max_delta

# 加载数据
def get_data_loaders(args):
    transform_train = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(svhn_mean, svhn_std)
    ])
    transform_test = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(svhn_mean, svhn_std)
    ])

    train_dataset = datasets.SVHN(args.data_dir, split='train', transform=transform_train, download=True)
    train_dataset = fix_svhn_labels(train_dataset)

    test_dataset = datasets.SVHN(args.data_dir, split='test', transform=transform_test, download=True)
    test_dataset = fix_svhn_labels(test_dataset)

    if args.val:
        logging.info("尝试加载预先生成的 SVHN 验证集划分...")
        val_split_file = os.path.join(args.data_dir, 'svhn_val_split.pth')

        val_base = datasets.SVHN(args.data_dir, split='train', transform=transform_test, download=True)
        val_base = fix_svhn_labels(val_base)

        if os.path.exists(val_split_file):
            split = torch.load(val_split_file)
            train_idx, val_idx = split['train_indices'], split['val_indices']
            from torch.utils.data import Subset
            train_dataset = Subset(train_dataset, train_idx)
            val_dataset = Subset(val_base, val_idx)
            logging.info("成功加载 SVHN 验证集划分")
        else:
            logging.info("从训练集中划分验证集...")
            indices = torch.randperm(len(train_dataset))
            split = int(0.9 * len(indices))
            train_idx, val_idx = indices[:split].tolist(), indices[split:].tolist()
            torch.save({'train_indices': train_idx, 'val_indices': val_idx}, val_split_file)
            from torch.utils.data import Subset
            train_dataset = Subset(train_dataset, train_idx)
            val_dataset = Subset(val_base, val_idx)

        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, pin_memory=True, num_workers=1)
        val_loader   = DataLoader(val_dataset,   batch_size=args.batch_size, shuffle=False, pin_memory=True, num_workers=1)
        test_loader  = DataLoader(test_dataset,  batch_size=args.batch_size, shuffle=False, pin_memory=True, num_workers=1)
        return train_loader, val_loader, test_loader
    else:
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, pin_memory=True, num_workers=1)
        test_loader  = DataLoader(test_dataset,  batch_size=args.batch_size, shuffle=False, pin_memory=True, num_workers=1)
        return train_loader, None, test_loader

# 主函数
def main():
    args = get_args()

    if not os.path.exists(args.fname):
        os.makedirs(args.fname)

    logger = logging.getLogger(__name__)
    logging.basicConfig(
        format='[%(asctime)s] - %(message)s',
        datefmt='%Y/%m/%d %H:%M:%S',
        level=logging.DEBUG,
        handlers=[
            logging.FileHandler(os.path.join(args.fname, 'eval.log' if args.eval else 'output.log')),
            logging.StreamHandler()
        ]
    )

    logger.info(args)

    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)

    if args.val:
        train_loader, val_loader, test_loader = get_data_loaders(args)
    else:
        train_loader, _, test_loader = get_data_loaders(args)
        val_loader = None

    # 选择模型
    if args.model == 'ResNet18':
        model = get_model(args.model, num_classes=10, in_channels=3)
    elif args.model == 'ResNet50':
        model = get_model(args.model, num_classes=10, in_channels=3)
    elif args.model == 'DenseNet121':
        model = get_model(args.model, num_classes=10, in_channels=3)
    elif args.model == 'PreActResNet18':
        model = PreActResNet18()
    elif args.model == 'WideResNet':
        model = WideResNet(34, 10, widen_factor=args.width_factor, dropRate=0.0)
    else:
        raise ValueError("未知模型类型")

    # GPU / DataParallel
    if args.no_dp or torch.cuda.device_count() == 1:
        model = model.cuda()
    else:
        model = nn.DataParallel(model).cuda()

    optimizer = optim.SGD(model.parameters(), lr=args.lr_max, momentum=0.9, weight_decay=5e-4)

    epsilon = args.epsilon / 255.
    pgd_alpha = args.pgd_alpha / 255.

    start_epoch = 0
    best_val_robust_acc = 0

    criterion = nn.CrossEntropyLoss()

    # 学习率调度器
    if args.lr_schedule == 'piecewise':
        def lr_schedule(t):
            if t < 100:
                return args.lr_max
            elif t < 150:
                return args.lr_max * 0.1
            else:
                return args.lr_max * 0.01
    elif args.lr_schedule == 'linear':
        def lr_schedule(t):
            return args.lr_max * (1 - t / args.epochs)
    elif args.lr_schedule == 'cosine':
        def lr_schedule(t):
            return args.lr_max * 0.5 * (1 + np.cos(t / args.epochs * np.pi))

    # 训练与评估循环保持不变
    for epoch in range(start_epoch, args.epochs):
        model.train()
        start_time = time.time()
        train_loss = 0
        train_acc = 0
        train_robust_loss = 0
        train_robust_acc = 0
        train_n = 0
        
        for i, (X, y) in enumerate(train_loader):
            if args.eval:
                break
            X, y = X.cuda(), y.cuda()
            
            if args.mixup:
                X, y_a, y_b, lam = mixup_data(X, y, args.mixup_alpha)
                X, y_a, y_b = map(Variable, (X, y_a, y_b))
            
            lr = lr_schedule(epoch + (i + 1) / len(train_loader))
            optimizer.param_groups[0].update(lr=lr)
            
            if args.attack == 'pgd':
                # Random initialization
                if args.mixup:
                    delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm, mixup=True, y_a=y_a, y_b=y_b, lam=lam)
                else:
                    delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                delta = delta.detach()
            elif args.attack == 'none':
                delta = torch.zeros_like(X)
            
            # 确保X+delta在[0,1]范围内，然后归一化
            X_adv = torch.clamp(X + delta[:X.size(0)], 0.0, 1.0)
            robust_output = model(normalize(X_adv))
            
            if args.mixup:
                robust_loss = mixup_criterion(criterion, robust_output, y_a, y_b, lam)
            else:
                robust_loss = criterion(robust_output, y)
            
            if args.l1:
                for name, param in model.parameters():
                    if 'bn' not in name and 'bias' not in name:
                        robust_loss += args.l1 * param.abs().sum()
            
            optimizer.zero_grad()
            robust_loss.backward()
            optimizer.step()
            
            # 评估干净样本的性能
            output = model(normalize(X))
            if args.mixup:
                loss = mixup_criterion(criterion, output, y_a, y_b, lam)
            else:
                loss = criterion(output, y)
            
            train_robust_loss += robust_loss.item() * y.size(0)
            train_robust_acc += (robust_output.max(1)[1] == y).sum().item()
            train_loss += loss.item() * y.size(0)
            train_acc += (output.max(1)[1] == y).sum().item()
            train_n += y.size(0)
        
        train_time = time.time() - start_time
        
        # 评估阶段
        model.eval()
        
        if val_loader is not None:
            val_loss = 0
            val_acc = 0
            val_robust_loss = 0
            val_robust_acc = 0
            val_n = 0
            
            for i, (X, y) in enumerate(val_loader):
                X, y = X.cuda(), y.cuda()
                
                with torch.no_grad():
                    output = model(normalize(X))
                    loss = F.cross_entropy(output, y)
                    val_loss += loss.item() * y.size(0)
                    val_acc += (output.max(1)[1] == y).sum().item()
                
                if args.attack != 'none':
                    delta = attack_pgd(model, X, y, epsilon, pgd_alpha, args.attack_iters, args.restarts, args.norm)
                    with torch.no_grad():
                        X_adv = torch.clamp(X + delta, 0.0, 1.0)
                        output = model(normalize(X_adv))
                        robust_loss = F.cross_entropy(output, y)
                        val_robust_loss += robust_loss.item() * y.size(0)
                        val_robust_acc += (output.max(1)[1] == y).sum().item()
                
                val_n += y.size(0)
            
            val_loss /= val_n
            val_acc = 100.0 * val_acc / val_n
            if args.attack != 'none':
                val_robust_loss /= val_n
                val_robust_acc = 100.0 * val_robust_acc / val_n
        
        # 计算训练指标
        train_loss /= train_n
        train_acc = 100.0 * train_acc / train_n
        
        test_loss = 0
        test_acc = 0
        test_robust_loss = 0
        test_robust_acc = 0
        
        if args.attack != 'none':
            train_robust_loss /= train_n
            train_robust_acc = 100.0 * train_robust_acc / train_n
        
        # 打印结果
        if val_loader is not None:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【验证指标】 Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【验证鲁棒】 Loss: {val_robust_loss:.4f}, Acc: {val_robust_acc:.2f}%")
        else:
            logger.info(f"Epoch: {epoch}, LR: {lr:.6f}")
            logger.info(f"【训练指标】 Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"【测试指标】 Loss: {test_loss:.4f}, Acc: {test_acc:.2f}%")
            if args.attack != 'none':
                logger.info(f"【训练鲁棒】 Loss: {train_robust_loss:.4f}, Acc: {train_robust_acc:.2f}%")
                logger.info(f"【测试鲁棒】 Loss: {test_robust_loss:.4f}, Acc: {test_robust_acc:.2f}%")
        
        # 保存模型检查点
        torch.save(model.state_dict(), os.path.join(args.fname, f'model_epoch{epoch}.pth'))
        
        # 保存最佳模型
        if args.attack != 'none' and val_loader is not None and val_robust_acc > best_val_robust_acc:
            best_val_robust_acc = val_robust_acc
            torch.save({
                'state_dict': model.state_dict(),
                'val_robust_acc': best_val_robust_acc,
                'epoch': epoch,
            }, os.path.join(args.fname, 'model_best.pth'))
        
        # 周期性保存
        if (epoch+1) % args.chkpt_iters == 0 or epoch+1 == args.epochs:
            torch.save(model.state_dict(), os.path.join(args.fname, f'model_{epoch}.pth'))
            torch.save(optimizer.state_dict(), os.path.join(args.fname, f'opt_{epoch}.pth'))

if __name__ == "__main__":
    main()
